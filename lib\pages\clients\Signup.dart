import 'package:flutter/material.dart';

void main() {
  runApp(FlutterApp());
}

class FlutterApp extends StatelessWidget {
  final ValueNotifier<bool> _dark = ValueNotifier<bool>(true);
  final ValueNotifier<double> _widthFactor = ValueNotifier<double>(1.0);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        home: ValueListenableBuilder<bool>(
            valueListenable: _dark,
            builder: (context, color, child) {
              return ValueListenableBuilder<double>(
                valueListenable: _widthFactor,
                builder: (context, factor, child) {
                  return Scaffold(
                      backgroundColor:
                          _dark.value ? Colors.black : Colors.white,
                      appBar: AppBar(
                        actions: [
                          Switch(
                            value: _dark.value,
                            onChanged: (value) {
                              _dark.value = value;
                            },
                          ),
                          DropdownButton<double>(
                            value: _widthFactor.value,
                            onChanged: (value) {
                              _widthFactor.value = value!;
                            },
                            items: [
                              DropdownMenuItem<double>(
                                value: 0.5,
                                child: Text('Size: 50%'),
                              ),
                              DropdownMenuItem<double>(
                                value: 0.75,
                                child: Text('Size: 75%'),
                              ),
                              DropdownMenuItem<double>(
                                value: 1.0,
                                child: Text('Size: 100%'),
                              ),
                            ],
                          ),
                        ],
                      ),
                      body: Center(
                          child: Container(
                        width: MediaQuery.of(context).size.width *
                            _widthFactor.value,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Signup(),
                          ],
                        ),
                      )));
                },
              );
            }));
  }
}

class Signup extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 402,
          height: 874,
          padding: const EdgeInsets.only(
            top: 48,
            left: 37,
            right: 37,
            bottom: 41,
          ),
          decoration: ShapeDecoration(
            gradient: LinearGradient(
              begin: Alignment(0.00, -1.00),
              end: Alignment(0, 1),
              colors: [Color(0xFFFFF7ED), Colors.white],
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 328,
                height: 168,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 64,
                      height: 64,
                      padding: const EdgeInsets.all(16),
                      decoration: ShapeDecoration(
                        color: Color(0xFFFFEDD5),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Container(
                              height: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 2.67, vertical: 4),
                              clipBehavior: Clip.antiAlias,
                              decoration: BoxDecoration(),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                ,
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: 328,
                      height: 32,
                      padding: const EdgeInsets.symmetric(horizontal: 77.92),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 172.16,
                            height: 32,
                            child: Text(
                              'Create Account',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Color(0xFF111827),
                                fontSize: 20.40,
                                fontFamily: 'Inter',
                                height: 0.08,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: 328,
                      height: 48,
                      padding: const EdgeInsets.only(
                        top: 1,
                        left: 31.88,
                        right: 31.88,
                        bottom: 2,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 264.25,
                            height: 21,
                            child: Text(
                              'Join thousands managing their family ',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Color(0xFF4B5563),
                                fontSize: 13.60,
                                fontFamily: 'Inter',
                                height: 0.13,
                              ),
                            ),
                          ),
                          const SizedBox(height: 3),
                          SizedBox(
                            width: 119.30,
                            height: 21,
                            child: Text(
                              'planning journey',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Color(0xFF4B5563),
                                fontSize: 13.60,
                                fontFamily: 'Inter',
                                height: 0.13,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              Container(
                width: double.infinity,
                height: 585,
                padding: const EdgeInsets.only(top: 25, left: 25, right: 25),
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    side: BorderSide(width: 1, color: Color(0xFFF3F4F6)),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  shadows: [
                    BoxShadow(
                      color: Color(0x14000000),
                      blurRadius: 40,
                      offset: Offset(4, 4),
                      spreadRadius: 0,
                    )BoxShadow(
                      color: Color(0x14000000),
                      blurRadius: 40,
                      offset: Offset(4, 4),
                      spreadRadius: 0,
                    )BoxShadow(
                      color: Color(0x14000000),
                      blurRadius: 40,
                      offset: Offset(4, 4),
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 278,
                      height: 560,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 430,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  height: 66,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        height: 20,
                                        padding: const EdgeInsets.only(right: 176, bottom: 1),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 102,
                                              height: 19,
                                              child: Text(
                                                'Full Name',
                                                style: TextStyle(
                                                  color: Color(0xFF374151),
                                                  fontSize: 16,
                                                  fontFamily: 'Inter',
                                                  height: 0.08,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 7),
                                      Container(
                                        width: double.infinity,
                                        height: 42,
                                        padding: const EdgeInsets.only(
                                          top: 8,
                                          left: 12,
                                          right: 146,
                                          bottom: 10,
                                        ),
                                        decoration: ShapeDecoration(
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(
                                              width: 2,
                                              strokeAlign: BorderSide.strokeAlignOutside,
                                              color: Colors.black.withOpacity(0.30000001192092896),
                                            ),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.end,
                                          children: [
                                            Container(
                                              width: 20,
                                              height: 20,
                                              padding: const EdgeInsets.symmetric(horizontal: 4.17, vertical: 2.50),
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  const SizedBox(height: 3.33),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(width: 26),
                                            Text(
                                              'John Doe',
                                              style: TextStyle(
                                                color: Color(0xFFCCCCCC),
                                                fontSize: 16,
                                                fontFamily: 'Inter',
                                                height: 0.09,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                                Container(
                                  height: 66,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        height: 20,
                                        padding: const EdgeInsets.only(right: 211, bottom: 1),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 67,
                                              height: 19,
                                              child: Text(
                                                'Email',
                                                style: TextStyle(
                                                  color: Color(0xFF374151),
                                                  fontSize: 16,
                                                  fontFamily: 'Inter',
                                                  height: 0.08,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Container(
                                        width: double.infinity,
                                        height: 42,
                                        padding: const EdgeInsets.only(
                                          top: 7,
                                          left: 12,
                                          right: 71,
                                          bottom: 11,
                                        ),
                                        decoration: ShapeDecoration(
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(
                                              width: 2,
                                              strokeAlign: BorderSide.strokeAlignOutside,
                                              color: Colors.black.withOpacity(0.30000001192092896),
                                            ),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.end,
                                          children: [
                                            Container(
                                              width: 20,
                                              height: 20,
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                ,
                                                ],
                                              ),
                                            ),
                                            const SizedBox(width: 24),
                                            Text(
                                              '<EMAIL>',
                                              style: TextStyle(
                                                color: Color(0xFFCCCCCC),
                                                fontSize: 16,
                                                fontFamily: 'Inter',
                                                height: 0.09,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                                Container(
                                  height: 66,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 278,
                                        height: 20,
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              width: 67,
                                              height: 19,
                                              child: Text(
                                                'Country',
                                                style: TextStyle(
                                                  color: Color(0xFF374151),
                                                  fontSize: 16,
                                                  fontFamily: 'Inter',
                                                  height: 0.08,
                                                ),
                                              ),
                                            ),
                                            Container(
                                              width: 20,
                                              height: 20,
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(),
                                              child: Stack(children: [
                                              ,
                                              ]),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Container(
                                        width: double.infinity,
                                        height: 42,
                                        padding: const EdgeInsets.only(top: 1, left: 39, right: 180),
                                        decoration: ShapeDecoration(
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(
                                              width: 2,
                                              strokeAlign: BorderSide.strokeAlignOutside,
                                              color: Colors.black.withOpacity(0.30000001192092896),
                                            ),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            Transform(
                                              transform: Matrix4.identity()..translate(0.0, 0.0)..rotateZ(1.57),
                                              child: Container(
                                                width: 41,
                                                decoration: ShapeDecoration(
                                                  shape: RoundedRectangleBorder(
                                                    side: BorderSide(
                                                      width: 2,
                                                      strokeAlign: BorderSide.strokeAlignCenter,
                                                      color: Colors.black.withOpacity(0.30000001192092896),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 17),
                                            Text(
                                              '+250',
                                              style: TextStyle(
                                                color: Color(0xFFCCCCCC),
                                                fontSize: 16,
                                                fontFamily: 'Inter',
                                                height: 0.09,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                                Container(
                                  height: 86,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        height: 20,
                                        padding: const EdgeInsets.only(right: 160, bottom: 1),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 118,
                                              height: 19,
                                              child: Text(
                                                'Password',
                                                style: TextStyle(
                                                  color: Color(0xFF374151),
                                                  fontSize: 16,
                                                  fontFamily: 'Inter',
                                                  height: 0.08,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Container(
                                        width: 278,
                                        height: 42,
                                        decoration: ShapeDecoration(
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(
                                              width: 2,
                                              strokeAlign: BorderSide.strokeAlignOutside,
                                              color: Colors.black.withOpacity(0.30000001192092896),
                                            ),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '••••••••',
                                              style: TextStyle(
                                                color: Color(0xFFCCCCCC),
                                                fontSize: 16,
                                                fontFamily: 'Inter',
                                                height: 0.09,
                                              ),
                                            ),
                                            Container(
                                              width: 20,
                                              height: 20,
                                              padding: const EdgeInsets.symmetric(horizontal: 2.50, vertical: 1.67),
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  const SizedBox(height: 0),
                                                ],
                                              ),
                                            ),
                                            Container(
                                              width: 20,
                                              height: 20,
                                              decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    width: 20,
                                                    height: 20,
                                                    clipBehavior: Clip.antiAlias,
                                                    decoration: BoxDecoration(),
                                                    child: Column(
                                                      mainAxisSize: MainAxisSize.min,
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                      ,
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Container(
                                        width: double.infinity,
                                        height: 16,
                                        padding: const EdgeInsets.only(right: 80),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 198,
                                              height: 16,
                                              child: Text(
                                                'Must be at least 8 characters',
                                                style: TextStyle(
                                                  color: Color(0xFFF29767),
                                                  fontSize: 13,
                                                  fontFamily: 'Inter',
                                                  height: 0.09,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                                Container(
                                  height: 66,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        height: 20,
                                        padding: const EdgeInsets.only(right: 91, bottom: 1),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 187,
                                              height: 19,
                                              child: Text(
                                                'Confirm Password',
                                                style: TextStyle(
                                                  color: Color(0xFF374151),
                                                  fontSize: 16,
                                                  fontFamily: 'Inter',
                                                  height: 0.08,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Container(
                                        width: 278,
                                        height: 42,
                                        decoration: ShapeDecoration(
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(
                                              width: 2,
                                              strokeAlign: BorderSide.strokeAlignOutside,
                                              color: Colors.black.withOpacity(0.30000001192092896),
                                            ),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '••••••••',
                                              style: TextStyle(
                                                color: Color(0xFFCCCCCC),
                                                fontSize: 16,
                                                fontFamily: 'Inter',
                                                height: 0.09,
                                              ),
                                            ),
                                            Container(
                                              width: 20,
                                              height: 20,
                                              padding: const EdgeInsets.symmetric(horizontal: 2.50, vertical: 1.67),
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  const SizedBox(height: 0),
                                                ],
                                              ),
                                            ),
                                            Container(
                                              width: 20,
                                              height: 20,
                                              decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    width: 20,
                                                    height: 20,
                                                    clipBehavior: Clip.antiAlias,
                                                    decoration: BoxDecoration(),
                                                    child: Column(
                                                      mainAxisSize: MainAxisSize.min,
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                      ,
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: 278,
                            height: 40,
                            padding: const EdgeInsets.only(
                              top: 9,
                              left: 84.92,
                              right: 84.92,
                              bottom: 10,
                            ),
                            decoration: ShapeDecoration(
                              color: Color(0xFFF97316),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 108.16,
                                  height: 21,
                                  child: Text(
                                    'Create Account',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 13.60,
                                      fontFamily: 'Inter',
                                      height: 0.13,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: 278,
                            padding: const EdgeInsets.only(
                              top: 3,
                              left: 37.25,
                              right: 37.27,
                              bottom: 1,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Container(
                                    height: 20,
                                    padding: const EdgeInsets.only(bottom: 1),
                                    decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 203.48,
                                          height: 19,
                                          child: Text.rich(
                                            TextSpan(
                                              children: [
                                                TextSpan(
                                                  text: 'Already have an account? ',
                                                  style: TextStyle(
                                                    color: Color(0xFFEA580C),
                                                    fontSize: 11.90,
                                                    fontFamily: 'Inter',
                                                    height: 0.14,
                                                  ),
                                                ),
                                                TextSpan(
                                                  text: 'Sign in',
                                                  style: TextStyle(
                                                    color: Color(0xFFEA580C),
                                                    fontSize: 11.90,
                                                    fontFamily: 'Inter',
                                                    height: 0.14,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}