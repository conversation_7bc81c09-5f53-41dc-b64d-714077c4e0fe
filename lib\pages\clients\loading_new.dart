import 'package:flutter/material.dart';
import 'dart:math' as math;

void main() {
  runApp(const FlutterApp());
}

class FlutterApp extends StatelessWidget {
  const FlutterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'HealthCare App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Inter',
        useMaterial3: true,
      ),
      home: const LoadingScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late AnimationController _fadeController;

  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Initialize animations
    _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.2).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    // Start animations
    _startAnimations();
  }

  void _startAnimations() async {
    // Start fade in
    _fadeController.forward();

    // Start scale animation
    await Future.delayed(const Duration(milliseconds: 300));
    _scaleController.forward();

    // Start rotation (continuous)
    _rotationController.repeat();

    // Start pulse (continuous)
    _pulseController.repeat(reverse: true);

    // Start progress
    await Future.delayed(const Duration(milliseconds: 500));
    _progressController.forward();

    // Navigate to splash screens after loading completes
    _progressController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/splash');
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    _progressController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFFF093FB)],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Animated Star Logo
                        AnimatedBuilder(
                          animation: Listenable.merge([
                            _rotationAnimation,
                            _scaleAnimation,
                            _pulseAnimation,
                          ]),
                          builder: (context, child) {
                            return Transform.scale(
                              scale:
                                  _scaleAnimation.value * _pulseAnimation.value,
                              child: Transform.rotate(
                                angle: _rotationAnimation.value,
                                child: Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.white.withOpacity(0.3),
                                        blurRadius: 20,
                                        spreadRadius: 5,
                                      ),
                                    ],
                                  ),
                                  child: CustomPaint(
                                    painter: UltraModernLogoPainter(),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 40),

                        // App Name
                        const Text(
                          'F.A.M.A.R.A.R',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 2.0,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 2),
                                blurRadius: 4,
                                color: Colors.black26,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Subtitle
                        const Text(
                          'Healthcare & Wellness',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                            letterSpacing: 1.0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Loading Progress Section
                Padding(
                  padding: const EdgeInsets.all(40.0),
                  child: Column(
                    children: [
                      // Loading Text
                      const Text(
                        'Preparing your experience...',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Progress Bar
                      AnimatedBuilder(
                        animation: _progressAnimation,
                        builder: (context, child) {
                          return Column(
                            children: [
                              LinearProgressIndicator(
                                value: _progressAnimation.value,
                                backgroundColor: Colors.white.withOpacity(0.2),
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                                minHeight: 3,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '${(_progressAnimation.value * 100).toInt()}%',
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class StarPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill
          ..strokeWidth = 2;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 3;

    final path = Path();

    // Create a 5-pointed star
    for (int i = 0; i < 5; i++) {
      final angle = (i * 2 * math.pi / 5) - (math.pi / 2);
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // Inner point
      final innerAngle = angle + (math.pi / 5);
      final innerRadius = radius * 0.4;
      final innerX = center.dx + innerRadius * math.cos(innerAngle);
      final innerY = center.dy + innerRadius * math.sin(innerAngle);
      path.lineTo(innerX, innerY);
    }

    path.close();
    canvas.drawPath(path, paint);

    // Add inner glow
    final glowPaint =
        Paint()
          ..color = Colors.yellow.withOpacity(0.6)
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.3, glowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ModernLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    // Create gradient paint
    final gradientPaint =
        Paint()
          ..shader = RadialGradient(
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.8),
              Colors.cyan.withOpacity(0.6),
            ],
            stops: const [0.0, 0.7, 1.0],
          ).createShader(Rect.fromCircle(center: center, radius: radius));

    // Outer ring
    final outerRingPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3;

    canvas.drawCircle(center, radius * 0.9, outerRingPaint);

    // Inner geometric design - Modern hexagon with cross
    _drawModernHexagon(canvas, center, radius * 0.7, gradientPaint);

    // Central cross/plus symbol
    _drawCentralCross(canvas, center, radius * 0.4);

    // Accent dots
    _drawAccentDots(canvas, center, radius * 0.85);
  }

  void _drawModernHexagon(
    Canvas canvas,
    Offset center,
    double radius,
    Paint paint,
  ) {
    final path = Path();

    // Create hexagon
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi / 3);
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas.drawPath(path, paint);

    // Add hexagon outline
    final outlinePaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    canvas.drawPath(path, outlinePaint);
  }

  void _drawCentralCross(Canvas canvas, Offset center, double size) {
    final crossPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    // Vertical bar
    final verticalRect = RRect.fromRectAndRadius(
      Rect.fromCenter(center: center, width: size * 0.3, height: size * 1.2),
      const Radius.circular(8),
    );
    canvas.drawRRect(verticalRect, crossPaint);

    // Horizontal bar
    final horizontalRect = RRect.fromRectAndRadius(
      Rect.fromCenter(center: center, width: size * 1.2, height: size * 0.3),
      const Radius.circular(8),
    );
    canvas.drawRRect(horizontalRect, crossPaint);

    // Add subtle inner shadow effect
    final shadowPaint =
        Paint()
          ..color = Colors.cyan.withOpacity(0.3)
          ..style = PaintingStyle.fill;

    final innerVertical = RRect.fromRectAndRadius(
      Rect.fromCenter(center: center, width: size * 0.15, height: size * 1.0),
      const Radius.circular(4),
    );
    canvas.drawRRect(innerVertical, shadowPaint);

    final innerHorizontal = RRect.fromRectAndRadius(
      Rect.fromCenter(center: center, width: size * 1.0, height: size * 0.15),
      const Radius.circular(4),
    );
    canvas.drawRRect(innerHorizontal, shadowPaint);
  }

  void _drawAccentDots(Canvas canvas, Offset center, double radius) {
    final dotPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    // Draw 4 accent dots at cardinal directions
    for (int i = 0; i < 4; i++) {
      final angle = (i * math.pi / 2) + (math.pi / 4);
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      canvas.drawCircle(Offset(x, y), 4, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ProfessionalLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.2;

    // Draw modern geometric logo with multiple layers
    _drawOuterRings(canvas, center, radius);
    _drawInnerGeometry(canvas, center, radius * 0.7);
    _drawCenterElement(canvas, center, radius * 0.35);
  }

  void _drawOuterRings(Canvas canvas, Offset center, double radius) {
    // Outer glow ring
    final glowPaint =
        Paint()
          ..color = Colors.white.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 8
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    canvas.drawCircle(center, radius * 0.95, glowPaint);

    // Main outer ring
    final outerPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3;

    canvas.drawCircle(center, radius * 0.9, outerPaint);

    // Inner accent ring
    final accentPaint =
        Paint()
          ..color = Colors.white.withOpacity(0.6)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    canvas.drawCircle(center, radius * 0.8, accentPaint);
  }

  void _drawInnerGeometry(Canvas canvas, Offset center, double radius) {
    // Create modern triangular/diamond pattern
    final geometryPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    // Draw 6 triangular segments forming a flower-like pattern
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi / 3);
      _drawTriangularSegment(canvas, center, radius, angle, geometryPaint);
    }

    // Add inner connecting lines
    final linePaint =
        Paint()
          ..color = Colors.white.withOpacity(0.8)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    for (int i = 0; i < 6; i++) {
      final angle1 = (i * math.pi / 3);
      final angle2 = ((i + 2) * math.pi / 3);

      final x1 = center.dx + (radius * 0.6) * math.cos(angle1);
      final y1 = center.dy + (radius * 0.6) * math.sin(angle1);
      final x2 = center.dx + (radius * 0.6) * math.cos(angle2);
      final y2 = center.dy + (radius * 0.6) * math.sin(angle2);

      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), linePaint);
    }
  }

  void _drawTriangularSegment(
    Canvas canvas,
    Offset center,
    double radius,
    double angle,
    Paint paint,
  ) {
    final path = Path();

    // Create triangular segment pointing outward
    final outerX = center.dx + radius * math.cos(angle);
    final outerY = center.dy + radius * math.sin(angle);

    final leftAngle = angle - (math.pi / 6);
    final rightAngle = angle + (math.pi / 6);

    final leftX = center.dx + (radius * 0.6) * math.cos(leftAngle);
    final leftY = center.dy + (radius * 0.6) * math.sin(leftAngle);

    final rightX = center.dx + (radius * 0.6) * math.cos(rightAngle);
    final rightY = center.dy + (radius * 0.6) * math.sin(rightAngle);

    path.moveTo(outerX, outerY);
    path.lineTo(leftX, leftY);
    path.lineTo(rightX, rightY);
    path.close();

    canvas.drawPath(path, paint);
  }

  void _drawCenterElement(Canvas canvas, Offset center, double radius) {
    // Central modern logo element - stylized medical cross
    final centerPaint =
        Paint()
          ..shader = RadialGradient(
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.9),
              Colors.cyan.withOpacity(0.4),
            ],
            stops: const [0.0, 0.6, 1.0],
          ).createShader(Rect.fromCircle(center: center, radius: radius));

    // Draw rounded cross
    final crossPath = Path();

    // Vertical bar
    crossPath.addRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: center,
          width: radius * 0.4,
          height: radius * 1.6,
        ),
        const Radius.circular(6),
      ),
    );

    // Horizontal bar
    crossPath.addRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: center,
          width: radius * 1.6,
          height: radius * 0.4,
        ),
        const Radius.circular(6),
      ),
    );

    canvas.drawPath(crossPath, centerPaint);

    // Add center highlight
    final highlightPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.2, highlightPaint);

    // Add subtle accent dots around center
    final dotPaint =
        Paint()
          ..color = Colors.white.withOpacity(0.8)
          ..style = PaintingStyle.fill;

    for (int i = 0; i < 4; i++) {
      final angle = (i * math.pi / 2) + (math.pi / 4);
      final x = center.dx + (radius * 0.8) * math.cos(angle);
      final y = center.dy + (radius * 0.8) * math.sin(angle);

      canvas.drawCircle(Offset(x, y), 2, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class UltraModernLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.1;

    // Ultra-modern minimalist design with dynamic elements
    _drawMinimalistRings(canvas, center, radius);
    _drawDynamicArcs(canvas, center, radius * 0.8);
    _drawCenterIcon(canvas, center, radius * 0.3);
  }

  void _drawMinimalistRings(Canvas canvas, Offset center, double radius) {
    // Outer subtle glow
    final glowPaint =
        Paint()
          ..color = Colors.white.withOpacity(0.2)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 12
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

    canvas.drawCircle(center, radius * 0.95, glowPaint);

    // Main ring with gradient effect
    final mainPaint =
        Paint()
          ..shader = SweepGradient(
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.8),
              Colors.cyan.withOpacity(0.6),
              Colors.white,
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ).createShader(Rect.fromCircle(center: center, radius: radius))
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2
          ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius * 0.85, mainPaint);
  }

  void _drawDynamicArcs(Canvas canvas, Offset center, double radius) {
    // Dynamic rotating arcs for modern tech feel
    final arcPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3
          ..strokeCap = StrokeCap.round;

    // Draw 3 dynamic arcs at different positions
    for (int i = 0; i < 3; i++) {
      final startAngle = (i * 2 * math.pi / 3) - (math.pi / 2);
      final sweepAngle = math.pi / 3;

      final rect = Rect.fromCircle(center: center, radius: radius - (i * 8));
      canvas.drawArc(rect, startAngle, sweepAngle, false, arcPaint);
    }

    // Inner accent arcs
    final accentPaint =
        Paint()
          ..color = Colors.white.withOpacity(0.6)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.5
          ..strokeCap = StrokeCap.round;

    for (int i = 0; i < 6; i++) {
      final startAngle = (i * math.pi / 3) + (math.pi / 6);
      final sweepAngle = math.pi / 8;

      final rect = Rect.fromCircle(center: center, radius: radius * 0.6);
      canvas.drawArc(rect, startAngle, sweepAngle, false, accentPaint);
    }
  }

  void _drawCenterIcon(Canvas canvas, Offset center, double radius) {
    // Modern minimalist health icon
    final iconPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    // Draw modern pulse/heartbeat icon
    final pulsePath = Path();

    // Start from left
    final startX = center.dx - radius;
    final startY = center.dy;

    pulsePath.moveTo(startX, startY);

    // Flat line
    pulsePath.lineTo(center.dx - radius * 0.4, startY);

    // First peak
    pulsePath.lineTo(center.dx - radius * 0.2, center.dy - radius * 0.6);
    pulsePath.lineTo(center.dx, center.dy + radius * 0.4);

    // Second peak
    pulsePath.lineTo(center.dx + radius * 0.2, center.dy - radius * 0.8);
    pulsePath.lineTo(center.dx + radius * 0.4, startY);

    // End flat line
    pulsePath.lineTo(center.dx + radius, startY);

    // Draw the pulse line with thickness
    final pulsePaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 4
          ..strokeCap = StrokeCap.round
          ..strokeJoin = StrokeJoin.round;

    canvas.drawPath(pulsePath, pulsePaint);

    // Add center dot
    final centerDotPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, 3, centerDotPaint);

    // Add subtle corner accents
    final accentPaint =
        Paint()
          ..color = Colors.white.withOpacity(0.7)
          ..style = PaintingStyle.fill;

    // Four corner dots
    final cornerRadius = radius * 0.9;
    for (int i = 0; i < 4; i++) {
      final angle = (i * math.pi / 2) + (math.pi / 4);
      final x = center.dx + cornerRadius * math.cos(angle);
      final y = center.dy + cornerRadius * math.sin(angle);

      canvas.drawCircle(Offset(x, y), 1.5, accentPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
