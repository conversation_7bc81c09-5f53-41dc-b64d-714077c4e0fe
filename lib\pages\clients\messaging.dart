import 'package:flutter/material.dart';

class MessagingScreen extends StatefulWidget {
  const MessagingScreen({super.key});

  @override
  State<MessagingScreen> createState() => _MessagingScreenState();
}

class _MessagingScreenState extends State<MessagingScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Current view: 'conversations' or 'chat'
  String _currentView = 'conversations';

  List<Map<String, dynamic>> _conversations = [
    {
      'id': '1',
      'name': 'Dr. <PERSON>',
      'title': 'Family Planning Specialist',
      'avatar': 'SJ',
      'lastMessage':
          'I\'ll review your cycle data and get back to you with recommendations.',
      'timestamp': '2 min ago',
      'unreadCount': 0,
      'isOnline': true,
      'userType': 'doctor',
    },
    {
      'id': '2',
      'name': 'Nurse <PERSON>',
      'title': 'Family Planning Counselor',
      'avatar': 'EW',
      'lastMessage': 'How are you feeling today? Any symptoms to report?',
      'timestamp': '15 min ago',
      'unreadCount': 2,
      'isOnline': true,
      'userType': 'nurse',
    },
    {
      'id': '3',
      'name': 'Dr. Michael Chen',
      'title': 'Reproductive Endocrinologist',
      'avatar': 'MC',
      'lastMessage':
          'Your hormone levels look normal. Let\'s schedule a follow-up.',
      'timestamp': '1 hour ago',
      'unreadCount': 0,
      'isOnline': false,
      'userType': 'doctor',
    },
    {
      'id': '4',
      'name': 'Health Support Team',
      'title': 'General Support',
      'avatar': 'HS',
      'lastMessage':
          'Welcome to our family planning support! How can we help you today?',
      'timestamp': '2 hours ago',
      'unreadCount': 1,
      'isOnline': true,
      'userType': 'support',
    },
  ];

  String? _selectedConversationId;
  List<Map<String, dynamic>> _messages = [];

  @override
  void initState() {
    super.initState();
    // Auto-select first conversation if available
    if (_conversations.isNotEmpty) {
      _selectConversation(_conversations.first['id']);
    }
  }

  void _selectConversation(String conversationId) {
    setState(() {
      _selectedConversationId = conversationId;
      _loadMessages(conversationId);
      _currentView = 'chat'; // Switch to chat view

      // Mark conversation as read
      final conversationIndex = _conversations.indexWhere(
        (c) => c['id'] == conversationId,
      );
      if (conversationIndex != -1) {
        _conversations[conversationIndex]['unreadCount'] = 0;
      }
    });
  }

  void _loadMessages(String conversationId) {
    // Sample messages for different conversations
    switch (conversationId) {
      case '1':
        _messages = [
          {
            'id': '1',
            'text':
                'Hello Dr. Johnson, I have some questions about my cycle tracking.',
            'isMe': true,
            'timestamp': '10:30 AM',
            'type': 'text',
          },
          {
            'id': '2',
            'text':
                'Hello! I\'d be happy to help you with your cycle tracking questions. What specific concerns do you have?',
            'isMe': false,
            'timestamp': '10:32 AM',
            'type': 'text',
          },
          {
            'id': '3',
            'text':
                'I\'ve been tracking for 3 months but my periods seem irregular. Is this normal?',
            'isMe': true,
            'timestamp': '10:35 AM',
            'type': 'text',
          },
          {
            'id': '4',
            'text':
                'Irregular periods can be normal, especially when you\'re just starting to track. Let me review your data and provide some insights.',
            'isMe': false,
            'timestamp': '10:37 AM',
            'type': 'text',
          },
          {
            'id': '5',
            'text':
                'I\'ll review your cycle data and get back to you with recommendations.',
            'isMe': false,
            'timestamp': '10:38 AM',
            'type': 'text',
          },
        ];
        break;
      case '2':
        _messages = [
          {
            'id': '1',
            'text': 'Hi Emma! I\'m experiencing some unusual symptoms today.',
            'isMe': true,
            'timestamp': '9:15 AM',
            'type': 'text',
          },
          {
            'id': '2',
            'text':
                'I\'m here to help! Can you describe the symptoms you\'re experiencing?',
            'isMe': false,
            'timestamp': '9:17 AM',
            'type': 'text',
          },
          {
            'id': '3',
            'text': 'How are you feeling today? Any symptoms to report?',
            'isMe': false,
            'timestamp': '9:45 AM',
            'type': 'text',
          },
        ];
        break;
      case '3':
        _messages = [
          {
            'id': '1',
            'text':
                'Dr. Chen, I received my hormone test results. Could you review them?',
            'isMe': true,
            'timestamp': 'Yesterday 2:30 PM',
            'type': 'text',
          },
          {
            'id': '2',
            'text':
                'I\'ve reviewed your results. Your hormone levels look normal. Let\'s schedule a follow-up.',
            'isMe': false,
            'timestamp': 'Yesterday 3:15 PM',
            'type': 'text',
          },
        ];
        break;
      case '4':
        _messages = [
          {
            'id': '1',
            'text':
                'Welcome to our family planning support! How can we help you today?',
            'isMe': false,
            'timestamp': '8:00 AM',
            'type': 'text',
          },
        ];
        break;
      default:
        _messages = [];
    }
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty ||
        _selectedConversationId == null) {
      return;
    }

    final newMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'text': _messageController.text.trim(),
      'isMe': true,
      'timestamp': _formatCurrentTime(),
      'type': 'text',
    };

    setState(() {
      _messages.add(newMessage);

      // Update conversation last message
      final conversationIndex = _conversations.indexWhere(
        (c) => c['id'] == _selectedConversationId,
      );
      if (conversationIndex != -1) {
        _conversations[conversationIndex]['lastMessage'] = newMessage['text'];
        _conversations[conversationIndex]['timestamp'] = 'now';
      }
    });

    _messageController.clear();
    _scrollToBottom();

    // Simulate health worker response after a delay
    Future.delayed(const Duration(seconds: 2), () {
      _simulateHealthWorkerResponse();
    });
  }

  void _simulateHealthWorkerResponse() {
    if (_selectedConversationId == null) return;

    final responses = [
      'Thank you for sharing that information. I\'ll review it and get back to you shortly.',
      'That\'s a great question! Let me provide you with some guidance on this.',
      'I understand your concern. Based on what you\'ve shared, here are my recommendations...',
      'Your health and well-being are our priority. Let\'s work together on this.',
      'I\'m here to support you. Feel free to ask any questions you might have.',
    ];

    final randomResponse =
        responses[DateTime.now().millisecondsSinceEpoch % responses.length];

    final responseMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'text': randomResponse,
      'isMe': false,
      'timestamp': _formatCurrentTime(),
      'type': 'text',
    };

    setState(() {
      _messages.add(responseMessage);

      // Update conversation last message
      final conversationIndex = _conversations.indexWhere(
        (c) => c['id'] == _selectedConversationId,
      );
      if (conversationIndex != -1) {
        _conversations[conversationIndex]['lastMessage'] =
            responseMessage['text'];
        _conversations[conversationIndex]['timestamp'] = 'now';
      }
    });

    _scrollToBottom();
  }

  String _formatCurrentTime() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  String _getSelectedConversationName() {
    if (_selectedConversationId == null) return 'Chat';
    final conversation = _conversations.firstWhere(
      (c) => c['id'] == _selectedConversationId,
      orElse: () => {'name': 'Chat'},
    );
    return conversation['name'] ?? 'Chat';
  }

  List<Map<String, dynamic>> _getFilteredConversations() {
    if (_searchController.text.isEmpty) {
      return _conversations;
    }

    final searchTerm = _searchController.text.toLowerCase();
    return _conversations.where((conversation) {
      final name = conversation['name'].toString().toLowerCase();
      final title = conversation['title'].toString().toLowerCase();
      final lastMessage = conversation['lastMessage'].toString().toLowerCase();

      return name.contains(searchTerm) ||
          title.contains(searchTerm) ||
          lastMessage.contains(searchTerm);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF374151)),
          onPressed: () {
            if (_currentView == 'chat') {
              setState(() {
                _currentView = 'conversations';
              });
            } else {
              Navigator.pop(context);
            }
          },
        ),
        title: Text(
          _currentView == 'conversations'
              ? 'Messages'
              : _getSelectedConversationName(),
          style: const TextStyle(
            color: Color(0xFF111827),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (_currentView == 'conversations')
            TextButton(
              onPressed: () {
                setState(() {
                  _currentView = 'chat';
                });
              },
              child: const Text(
                'Chat',
                style: TextStyle(
                  color: Color(0xFFF97316),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          if (_currentView == 'chat')
            TextButton(
              onPressed: () {
                setState(() {
                  _currentView = 'conversations';
                });
              },
              child: const Text(
                'Back',
                style: TextStyle(
                  color: Color(0xFFF97316),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body:
          _currentView == 'conversations'
              ? _buildConversationsTab()
              : _buildChatTab(),
    );
  }

  Widget _buildConversationsTab() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  // Trigger rebuild to filter conversations
                });
              },
              decoration: InputDecoration(
                hintText: 'Search conversations...',
                hintStyle: const TextStyle(color: Color(0xFF9CA3AF)),
                prefixIcon: const Icon(Icons.search, color: Color(0xFF9CA3AF)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFFF97316)),
                ),
                filled: true,
                fillColor: const Color(0xFFF9FAFB),
              ),
            ),
          ),
          // Conversations list
          Expanded(child: _buildConversationsList()),
        ],
      ),
    );
  }

  Widget _buildConversationsList() {
    final filteredConversations = _getFilteredConversations();

    return ListView.builder(
      itemCount: filteredConversations.length,
      itemBuilder: (context, index) {
        final conversation = filteredConversations[index];
        final isSelected = conversation['id'] == _selectedConversationId;

        return GestureDetector(
          onTap: () {
            _selectConversation(conversation['id']);
            // View switching is handled in _selectConversation method
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFFFEF7ED) : Colors.white,
              border: const Border(
                bottom: BorderSide(color: Color(0xFFE2E8F0), width: 0.5),
              ),
            ),
            child: Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 26,
                      backgroundColor: _getAvatarColor(
                        conversation['userType'],
                      ),
                      child: Text(
                        conversation['avatar'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                      ),
                    ),
                    if (conversation['isOnline'])
                      Positioned(
                        right: 2,
                        bottom: 2,
                        child: Container(
                          width: 14,
                          height: 14,
                          decoration: BoxDecoration(
                            color: const Color(0xFF10B981),
                            border: Border.all(color: Colors.white, width: 2.5),
                            borderRadius: BorderRadius.circular(7),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              conversation['name'],
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF0F172A),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (conversation['unreadCount'] > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF97316),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                conversation['unreadCount'].toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        conversation['title'],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFFF97316),
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        conversation['lastMessage'],
                        style: const TextStyle(
                          fontSize: 13,
                          color: Color(0xFF64748B),
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        conversation['timestamp'],
                        style: const TextStyle(
                          fontSize: 11,
                          color: Color(0xFF94A3B8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getAvatarColor(String userType) {
    switch (userType) {
      case 'doctor':
        return const Color(0xFF7C3AED);
      case 'nurse':
        return const Color(0xFF059669);
      case 'support':
        return const Color(0xFF0891B2);
      default:
        return const Color(0xFFF97316);
    }
  }

  Widget _buildChatTab() {
    if (_selectedConversationId == null) {
      return Container(
        color: const Color(0xFFF8FAFC),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 80,
                color: Color(0xFFCBD5E1),
              ),
              SizedBox(height: 24),
              Text(
                'Select a conversation',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF475569),
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Choose a health worker from the Conversations tab to start chatting',
                style: TextStyle(fontSize: 15, color: Color(0xFF94A3B8)),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    final conversation = _conversations.firstWhere(
      (c) => c['id'] == _selectedConversationId,
    );

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8FAFC), Color(0xFFF1F5F9)],
        ),
      ),
      child: Column(
        children: [
          _buildChatHeader(conversation),
          Expanded(child: _buildMessagesList()),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildChatHeader(Map<String, dynamic> conversation) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0), width: 1)),
        boxShadow: [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Stack(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: _getAvatarColor(conversation['userType']),
                child: Text(
                  conversation['avatar'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              if (conversation['isOnline'])
                Positioned(
                  right: 2,
                  bottom: 2,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: const Color(0xFF10B981),
                      border: Border.all(color: Colors.white, width: 2.5),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  conversation['name'],
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF0F172A),
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color:
                            conversation['isOnline']
                                ? const Color(0xFF10B981)
                                : const Color(0xFF94A3B8),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      conversation['isOnline'] ? 'Online' : 'Offline',
                      style: TextStyle(
                        fontSize: 13,
                        color:
                            conversation['isOnline']
                                ? const Color(0xFF10B981)
                                : const Color(0xFF64748B),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F5F9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.videocam,
                  color: Color(0xFF475569),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F5F9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.phone,
                  color: Color(0xFF475569),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F5F9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.more_vert,
                  color: Color(0xFF475569),
                  size: 20,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message) {
    final isMe = message['isMe'] as bool;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 18,
              backgroundColor: const Color(0xFF7C3AED),
              child: const Icon(Icons.person, color: Colors.white, size: 18),
            ),
            const SizedBox(width: 12),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.45,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14),
              decoration: BoxDecoration(
                color: isMe ? const Color(0xFFF97316) : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isMe ? 20 : 6),
                  bottomRight: Radius.circular(isMe ? 6 : 20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 12,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message['text'],
                    style: TextStyle(
                      fontSize: 15,
                      color: isMe ? Colors.white : const Color(0xFF0F172A),
                      height: 1.5,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    message['timestamp'],
                    style: TextStyle(
                      fontSize: 11,
                      color:
                          isMe
                              ? Colors.white.withValues(alpha: 0.8)
                              : const Color(0xFF64748B),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 12),
            CircleAvatar(
              radius: 18,
              backgroundColor: const Color(0xFF059669),
              child: const Icon(Icons.person, color: Colors.white, size: 18),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE2E8F0), width: 1)),
        boxShadow: [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F5F9),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.attach_file,
              color: Color(0xFF475569),
              size: 22,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF8FAFC),
                borderRadius: BorderRadius.circular(28),
                border: Border.all(color: const Color(0xFFE2E8F0)),
              ),
              child: TextField(
                controller: _messageController,
                decoration: const InputDecoration(
                  hintText: 'Type your message...',
                  hintStyle: TextStyle(color: Color(0xFF94A3B8), fontSize: 15),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 14,
                  ),
                ),
                style: const TextStyle(fontSize: 15, color: Color(0xFF0F172A)),
                maxLines: null,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: const Color(0xFFF97316),
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFF97316).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(Icons.send, color: Colors.white, size: 22),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}
