import 'package:flutter/material.dart';

class MessagingScreen extends StatefulWidget {
  const MessagingScreen({super.key});

  @override
  State<MessagingScreen> createState() => _MessagingScreenState();
}

class _MessagingScreenState extends State<MessagingScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> _conversations = [
    {
      'id': '1',
      'name': 'Dr. <PERSON>',
      'title': 'Family Planning Specialist',
      'avatar': 'SJ',
      'lastMessage':
          'I\'ll review your cycle data and get back to you with recommendations.',
      'timestamp': '2 min ago',
      'unreadCount': 0,
      'isOnline': true,
      'userType': 'doctor',
    },
    {
      'id': '2',
      'name': 'Nurse <PERSON>',
      'title': 'Family Planning Counselor',
      'avatar': 'EW',
      'lastMessage': 'How are you feeling today? Any symptoms to report?',
      'timestamp': '15 min ago',
      'unreadCount': 2,
      'isOnline': true,
      'userType': 'nurse',
    },
    {
      'id': '3',
      'name': 'Dr. <PERSON>',
      'title': 'Reproductive Endocrinologist',
      'avatar': 'MC',
      'lastMessage':
          'Your hormone levels look normal. Let\'s schedule a follow-up.',
      'timestamp': '1 hour ago',
      'unreadCount': 0,
      'isOnline': false,
      'userType': 'doctor',
    },
    {
      'id': '4',
      'name': 'Health Support Team',
      'title': 'General Support',
      'avatar': 'HS',
      'lastMessage':
          'Welcome to our family planning support! How can we help you today?',
      'timestamp': '2 hours ago',
      'unreadCount': 1,
      'isOnline': true,
      'userType': 'support',
    },
  ];

  String? _selectedConversationId;
  List<Map<String, dynamic>> _messages = [];

  @override
  void initState() {
    super.initState();
    // Auto-select first conversation if available
    if (_conversations.isNotEmpty) {
      _selectConversation(_conversations.first['id']);
    }
  }

  void _selectConversation(String conversationId) {
    setState(() {
      _selectedConversationId = conversationId;
      _loadMessages(conversationId);

      // Mark conversation as read
      final conversationIndex = _conversations.indexWhere(
        (c) => c['id'] == conversationId,
      );
      if (conversationIndex != -1) {
        _conversations[conversationIndex]['unreadCount'] = 0;
      }
    });
  }

  void _loadMessages(String conversationId) {
    // Sample messages for different conversations
    switch (conversationId) {
      case '1':
        _messages = [
          {
            'id': '1',
            'text':
                'Hello Dr. Johnson, I have some questions about my cycle tracking.',
            'isMe': true,
            'timestamp': '10:30 AM',
            'type': 'text',
          },
          {
            'id': '2',
            'text':
                'Hello! I\'d be happy to help you with your cycle tracking questions. What specific concerns do you have?',
            'isMe': false,
            'timestamp': '10:32 AM',
            'type': 'text',
          },
          {
            'id': '3',
            'text':
                'I\'ve been tracking for 3 months but my periods seem irregular. Is this normal?',
            'isMe': true,
            'timestamp': '10:35 AM',
            'type': 'text',
          },
          {
            'id': '4',
            'text':
                'Irregular periods can be normal, especially when you\'re just starting to track. Let me review your data and provide some insights.',
            'isMe': false,
            'timestamp': '10:37 AM',
            'type': 'text',
          },
          {
            'id': '5',
            'text':
                'I\'ll review your cycle data and get back to you with recommendations.',
            'isMe': false,
            'timestamp': '10:38 AM',
            'type': 'text',
          },
        ];
        break;
      case '2':
        _messages = [
          {
            'id': '1',
            'text': 'Hi Emma! I\'m experiencing some unusual symptoms today.',
            'isMe': true,
            'timestamp': '9:15 AM',
            'type': 'text',
          },
          {
            'id': '2',
            'text':
                'I\'m here to help! Can you describe the symptoms you\'re experiencing?',
            'isMe': false,
            'timestamp': '9:17 AM',
            'type': 'text',
          },
          {
            'id': '3',
            'text': 'How are you feeling today? Any symptoms to report?',
            'isMe': false,
            'timestamp': '9:45 AM',
            'type': 'text',
          },
        ];
        break;
      case '3':
        _messages = [
          {
            'id': '1',
            'text':
                'Dr. Chen, I received my hormone test results. Could you review them?',
            'isMe': true,
            'timestamp': 'Yesterday 2:30 PM',
            'type': 'text',
          },
          {
            'id': '2',
            'text':
                'I\'ve reviewed your results. Your hormone levels look normal. Let\'s schedule a follow-up.',
            'isMe': false,
            'timestamp': 'Yesterday 3:15 PM',
            'type': 'text',
          },
        ];
        break;
      case '4':
        _messages = [
          {
            'id': '1',
            'text':
                'Welcome to our family planning support! How can we help you today?',
            'isMe': false,
            'timestamp': '8:00 AM',
            'type': 'text',
          },
        ];
        break;
      default:
        _messages = [];
    }
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty ||
        _selectedConversationId == null)
      return;

    final newMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'text': _messageController.text.trim(),
      'isMe': true,
      'timestamp': _formatCurrentTime(),
      'type': 'text',
    };

    setState(() {
      _messages.add(newMessage);

      // Update conversation last message
      final conversationIndex = _conversations.indexWhere(
        (c) => c['id'] == _selectedConversationId,
      );
      if (conversationIndex != -1) {
        _conversations[conversationIndex]['lastMessage'] = newMessage['text'];
        _conversations[conversationIndex]['timestamp'] = 'now';
      }
    });

    _messageController.clear();
    _scrollToBottom();

    // Simulate health worker response after a delay
    Future.delayed(const Duration(seconds: 2), () {
      _simulateHealthWorkerResponse();
    });
  }

  void _simulateHealthWorkerResponse() {
    if (_selectedConversationId == null) return;

    final responses = [
      'Thank you for sharing that information. I\'ll review it and get back to you shortly.',
      'That\'s a great question! Let me provide you with some guidance on this.',
      'I understand your concern. Based on what you\'ve shared, here are my recommendations...',
      'Your health and well-being are our priority. Let\'s work together on this.',
      'I\'m here to support you. Feel free to ask any questions you might have.',
    ];

    final randomResponse =
        responses[DateTime.now().millisecondsSinceEpoch % responses.length];

    final responseMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'text': randomResponse,
      'isMe': false,
      'timestamp': _formatCurrentTime(),
      'type': 'text',
    };

    setState(() {
      _messages.add(responseMessage);

      // Update conversation last message
      final conversationIndex = _conversations.indexWhere(
        (c) => c['id'] == _selectedConversationId,
      );
      if (conversationIndex != -1) {
        _conversations[conversationIndex]['lastMessage'] =
            responseMessage['text'];
        _conversations[conversationIndex]['timestamp'] = 'now';
      }
    });

    _scrollToBottom();
  }

  String _formatCurrentTime() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      body: SafeArea(
        child: Row(
          children: [
            // Conversations List (Left Side)
            Container(
              width: MediaQuery.of(context).size.width * 0.35,
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  right: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Column(
                children: [
                  _buildConversationsHeader(),
                  Expanded(child: _buildConversationsList()),
                ],
              ),
            ),

            // Chat Area (Right Side)
            Expanded(
              child:
                  _selectedConversationId != null
                      ? _buildChatArea()
                      : _buildEmptyState(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationsHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1)),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFF3F4F6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Color(0xFF374151),
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Messages',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF111827),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFF3F4F6),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.add, color: Color(0xFF374151), size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationsList() {
    return ListView.builder(
      itemCount: _conversations.length,
      itemBuilder: (context, index) {
        final conversation = _conversations[index];
        final isSelected = conversation['id'] == _selectedConversationId;

        return GestureDetector(
          onTap: () => _selectConversation(conversation['id']),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFFFEF3E2) : Colors.white,
              border: const Border(
                bottom: BorderSide(color: Color(0xFFE5E7EB), width: 0.5),
              ),
            ),
            child: Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 24,
                      backgroundColor: _getAvatarColor(
                        conversation['userType'],
                      ),
                      child: Text(
                        conversation['avatar'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    if (conversation['isOnline'])
                      Positioned(
                        right: 0,
                        bottom: 0,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: const Color(0xFF10B981),
                            border: Border.all(color: Colors.white, width: 2),
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              conversation['name'],
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF111827),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (conversation['unreadCount'] > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF97316),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                conversation['unreadCount'].toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Text(
                        conversation['title'],
                        style: const TextStyle(
                          fontSize: 11,
                          color: Color(0xFFF97316),
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        conversation['lastMessage'],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        conversation['timestamp'],
                        style: const TextStyle(
                          fontSize: 10,
                          color: Color(0xFF9CA3AF),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getAvatarColor(String userType) {
    switch (userType) {
      case 'doctor':
        return const Color(0xFF7C3AED);
      case 'nurse':
        return const Color(0xFF059669);
      case 'support':
        return const Color(0xFF0891B2);
      default:
        return const Color(0xFFF97316);
    }
  }

  Widget _buildEmptyState() {
    return Container(
      color: const Color(0xFFF9FAFB),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Color(0xFFD1D5DB)),
            SizedBox(height: 16),
            Text(
              'Select a conversation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF6B7280),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Choose a health worker to start chatting',
              style: TextStyle(fontSize: 14, color: Color(0xFF9CA3AF)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatArea() {
    final conversation = _conversations.firstWhere(
      (c) => c['id'] == _selectedConversationId,
    );

    return Container(
      color: const Color(0xFFF9FAFB),
      child: Column(
        children: [
          _buildChatHeader(conversation),
          Expanded(child: _buildMessagesList()),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildChatHeader(Map<String, dynamic> conversation) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1)),
      ),
      child: Row(
        children: [
          Stack(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: _getAvatarColor(conversation['userType']),
                child: Text(
                  conversation['avatar'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              if (conversation['isOnline'])
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: const Color(0xFF10B981),
                      border: Border.all(color: Colors.white, width: 2),
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  conversation['name'],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF111827),
                  ),
                ),
                Text(
                  conversation['isOnline'] ? 'Online' : 'Offline',
                  style: TextStyle(
                    fontSize: 12,
                    color:
                        conversation['isOnline']
                            ? const Color(0xFF10B981)
                            : const Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F6),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.videocam,
                  color: Color(0xFF374151),
                  size: 20,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F6),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.phone,
                  color: Color(0xFF374151),
                  size: 20,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F6),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.more_vert,
                  color: Color(0xFF374151),
                  size: 20,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message) {
    final isMe = message['isMe'] as bool;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: const Color(0xFFF97316),
              child: const Icon(Icons.person, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isMe ? const Color(0xFFF97316) : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isMe ? 16 : 4),
                  bottomRight: Radius.circular(isMe ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message['text'],
                    style: TextStyle(
                      fontSize: 14,
                      color: isMe ? Colors.white : const Color(0xFF111827),
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message['timestamp'],
                    style: TextStyle(
                      fontSize: 10,
                      color:
                          isMe
                              ? Colors.white.withValues(alpha: 0.8)
                              : const Color(0xFF6B7280),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: const Color(0xFF059669),
              child: const Icon(Icons.person, color: Colors.white, size: 16),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE5E7EB), width: 1)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFF3F4F6),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.attach_file,
              color: Color(0xFF374151),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF9FAFB),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(color: const Color(0xFFE5E7EB)),
              ),
              child: TextField(
                controller: _messageController,
                decoration: const InputDecoration(
                  hintText: 'Type your message...',
                  hintStyle: TextStyle(color: Color(0xFF9CA3AF)),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                maxLines: null,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF97316),
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(Icons.send, color: Colors.white, size: 20),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
