import 'package:flutter/material.dart';

void main() {
  runApp(FlutterApp());
}

class FlutterApp extends StatelessWidget {
  final ValueNotifier<bool> _dark = ValueNotifier<bool>(true);
  final ValueNotifier<double> _widthFactor = ValueNotifier<double>(1.0);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        home: ValueListenableBuilder<bool>(
            valueListenable: _dark,
            builder: (context, color, child) {
              return ValueListenableBuilder<double>(
                valueListenable: _widthFactor,
                builder: (context, factor, child) {
                  return Scaffold(
                      backgroundColor:
                          _dark.value ? Colors.black : Colors.white,
                      appBar: AppBar(
                        actions: [
                          Switch(
                            value: _dark.value,
                            onChanged: (value) {
                              _dark.value = value;
                            },
                          ),
                          DropdownButton<double>(
                            value: _widthFactor.value,
                            onChanged: (value) {
                              _widthFactor.value = value!;
                            },
                            items: [
                              DropdownMenuItem<double>(
                                value: 0.5,
                                child: Text('Size: 50%'),
                              ),
                              DropdownMenuItem<double>(
                                value: 0.75,
                                child: Text('Size: 75%'),
                              ),
                              DropdownMenuItem<double>(
                                value: 1.0,
                                child: Text('Size: 100%'),
                              ),
                            ],
                          ),
                        ],
                      ),
                      body: Center(
                          child: Container(
                        width: MediaQuery.of(context).size.width *
                            _widthFactor.value,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Splash2(),
                          ],
                        ),
                      )));
                },
              );
            }));
  }
}

class Splash2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 403,
          height: 873,
          padding: const EdgeInsets.only(right: 1),
          decoration: BoxDecoration(color: Colors.white),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  width: double.infinity,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(color: Color(0xFFFFEDD5)),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                width: 403,
                                height: 874,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 311,
                                      height: 4,
                                      padding: const EdgeInsets.symmetric(horizontal: 99.50),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            width: 32,
                                            height: 4,
                                            decoration: ShapeDecoration(
                                              color: Color(0xFFFB923C),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(9999),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Container(
                                            width: 32,
                                            height: 4,
                                            decoration: ShapeDecoration(
                                              color: Color(0xFFF97316),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(9999),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Container(
                                            width: 32,
                                            height: 4,
                                            decoration: ShapeDecoration(
                                              color: Color(0xFFFED7AA),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(9999),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 311,
                                      height: 311,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(24),
                                        ),
                                        shadows: [
                                          BoxShadow(
                                            color: Color(0x00000000),
                                            blurRadius: 0,
                                            offset: Offset(0, 0),
                                            spreadRadius: 0,
                                          )BoxShadow(
                                            color: Color(0x00000000),
                                            blurRadius: 0,
                                            offset: Offset(0, 0),
                                            spreadRadius: 0,
                                          )BoxShadow(
                                            color: Color(0x19000000),
                                            blurRadius: 25,
                                            offset: Offset(0, 20),
                                            spreadRadius: -5,
                                          )BoxShadow(
                                            color: Color(0x19000000),
                                            blurRadius: 10,
                                            offset: Offset(0, 8),
                                            spreadRadius: -6,
                                          )
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                            width: 311,
                                            height: 311,
                                            decoration: BoxDecoration(
                                              image: DecorationImage(
                                                image: NetworkImage("https://picsum.photos/311/311"),
                                                fit: BoxFit.fill,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 80,
                                      height: 80,
                                      padding: const EdgeInsets.all(20),
                                      decoration: ShapeDecoration(
                                        color: Color(0xFFF4D8B2),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(9999),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Expanded(
                                            child: Container(
                                              height: double.infinity,
                                              padding: const EdgeInsets.symmetric(horizontal: 3.33, vertical: 5),
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                ,
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 311,
                                      height: 88,
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            width: double.infinity,
                                            height: 32,
                                            padding: const EdgeInsets.symmetric(horizontal: 66.81),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                  width: 177.38,
                                                  height: 32,
                                                  child: Text(
                                                    'Health Tracking',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFF111827),
                                                      fontSize: 20.40,
                                                      fontFamily: 'Inter',
                                                      height: 0.08,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Expanded(
                                            child: Container(
                                              width: double.infinity,
                                              padding: const EdgeInsets.only(
                                                top: 1,
                                                left: 32.55,
                                                right: 32.56,
                                                bottom: 2,
                                              ),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  SizedBox(
                                                    width: 245.89,
                                                    height: 21,
                                                    child: Text(
                                                      'Monitor your cycle and health with ',
                                                      textAlign: TextAlign.center,
                                                      style: TextStyle(
                                                        color: Color(0xFF4B5563),
                                                        fontSize: 15,
                                                        fontFamily: 'Inter',
                                                        height: 0.11,
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 3),
                                                  SizedBox(
                                                    width: 131.81,
                                                    height: 21,
                                                    child: Text(
                                                      'intelligent tracking',
                                                      textAlign: TextAlign.center,
                                                      style: TextStyle(
                                                        color: Color(0xFF4B5563),
                                                        fontSize: 15,
                                                        fontFamily: 'Inter',
                                                        height: 0.11,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                            width: 85.72,
                                            height: 24,
                                            padding: const EdgeInsets.only(top: 1, bottom: 2),
                                            decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: 20,
                                                  height: double.infinity,
                                                  padding: const EdgeInsets.symmetric(vertical: 5),
                                                  clipBehavior: Clip.antiAlias,
                                                  decoration: BoxDecoration(),
                                                  child: Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    crossAxisAlignment: CrossAxisAlignment.center,
                                                    children: [
                                                    ,
                                                    ],
                                                  ),
                                                ),
                                                const SizedBox(width: 4),
                                                SizedBox(
                                                  width: 61.72,
                                                  height: 21,
                                                  child: Text(
                                                    'Previous',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFFEA580C),
                                                      fontSize: 13.60,
                                                      fontFamily: 'Inter',
                                                      height: 0.13,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 68),
                                          Container(
                                            width: 30.94,
                                            height: 24,
                                            padding: const EdgeInsets.only(top: 1, bottom: 2),
                                            decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                  width: 30.94,
                                                  height: 21,
                                                  child: Text(
                                                    'Skip',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFFEA580C),
                                                      fontSize: 13.60,
                                                      fontFamily: 'Inter',
                                                      height: 0.13,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 68),
                                          Container(
                                            width: 58.58,
                                            height: 24,
                                            padding: const EdgeInsets.only(top: 1, bottom: 2),
                                            decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                  width: 34.58,
                                                  height: 21,
                                                  child: Text(
                                                    'Next',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFFEA580C),
                                                      fontSize: 13.60,
                                                      fontFamily: 'Inter',
                                                      height: 0.13,
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(width: 4),
                                                Container(
                                                  width: 20,
                                                  height: double.infinity,
                                                  padding: const EdgeInsets.symmetric(vertical: 5),
                                                  clipBehavior: Clip.antiAlias,
                                                  decoration: BoxDecoration(),
                                                  child: Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    crossAxisAlignment: CrossAxisAlignment.center,
                                                    children: [
                                                    ,
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}