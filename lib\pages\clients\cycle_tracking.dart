import 'package:flutter/material.dart';

void main() {
  runApp(const FigmaToCodeApp());
}

// Generated by: https://www.figma.com/community/plugin/842128343887142055/
class FigmaToCodeApp extends StatelessWidget {
  const FigmaToCodeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData.dark().copyWith(
        scaffoldBackgroundColor: const Color.fromARGB(255, 18, 32, 47),
      ),
      home: Scaffold(
        body: ListView(children: [
          CycleTracking(),
        ]),
      ),
    );
  }
}

class CycleTracking extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 402,
          height: 1685,
          decoration: BoxDecoration(color: Colors.white),
          child: Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                child: Container(
                  width: 402,
                  height: 1685,
                  child: Stack(
                    children: [
                      Positioned(
                        left: 0,
                        top: 0,
                        child: Container(
                          width: 402,
                          height: 1685,
                          decoration: BoxDecoration(color: const Color(0xFFF9FAFB)),
                          child: Stack(
                            children: [
                              Positioned(
                                left: 16,
                                top: 16,
                                child: Container(
                                  width: 66.84,
                                  height: 40,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(9999),
                                    ),
                                    shadows: [
                                      BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x19000000),
                                        blurRadius: 15,
                                        offset: Offset(0, 10),
                                        spreadRadius: -3,
                                      )BoxShadow(
                                        color: Color(0x19000000),
                                        blurRadius: 6,
                                        offset: Offset(0, 4),
                                        spreadRadius: -4,
                                      )
                                    ],
                                  ),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 8,
                                        top: 9,
                                        child: SizedBox(
                                          width: 50.84,
                                          height: 21,
                                          child: Text(
                                            '← Back',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13.60,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              height: 1.76,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 0,
                                top: 0,
                                child: Container(
                                  width: 358,
                                  height: 1685,
                                  decoration: BoxDecoration(color: Colors.white),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 0,
                                        top: 0,
                                        child: Container(
                                          width: 401,
                                          height: 236,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment(0.00, 0.50),
                                              end: Alignment(1.00, 0.50),
                                              colors: [const Color(0xFFF97316), const Color(0xFFEA580C)],
                                            ),
                                          ),
                                          child: Stack(
                                            children: [
                                              Positioned(
                                                left: 32,
                                                top: 32,
                                                child: Container(
                                                  width: 294,
                                                  height: 172,
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 0,
                                                        top: 0,
                                                        child: Container(
                                                          width: 294,
                                                          height: 72,
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 0,
                                                                top: -2,
                                                                child: SizedBox(
                                                                  width: 220.98,
                                                                  height: 40,
                                                                  child: Text(
                                                                    'Advanced Cycle ',
                                                                    style: TextStyle(
                                                                      color: Colors.white,
                                                                      fontSize: 25.50,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w700,
                                                                      height: 1.41,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 0,
                                                                top: 34,
                                                                child: SizedBox(
                                                                  width: 119.83,
                                                                  height: 40,
                                                                  child: Text(
                                                                    'Tracking',
                                                                    style: TextStyle(
                                                                      color: Colors.white,
                                                                      fontSize: 25.50,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w700,
                                                                      height: 1.41,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 0,
                                                        top: 88,
                                                        child: Container(
                                                          width: 294,
                                                          height: 84,
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 0,
                                                                top: 2,
                                                                child: SizedBox(
                                                                  width: 271.16,
                                                                  height: 24,
                                                                  child: Text(
                                                                    'Monitor your menstrual cycle with ',
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFFFEDD5),
                                                                      fontSize: 15.30,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.83,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 0,
                                                                top: 30,
                                                                child: SizedBox(
                                                                  width: 207.61,
                                                                  height: 24,
                                                                  child: Text(
                                                                    'intelligent predictions and ',
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFFFEDD5),
                                                                      fontSize: 15.30,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.83,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 0,
                                                                top: 58,
                                                                child: SizedBox(
                                                                  width: 166.66,
                                                                  height: 24,
                                                                  child: Text(
                                                                    'personalized insights',
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFFFEDD5),
                                                                      fontSize: 15.30,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.83,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 0,
                                        top: 236,
                                        child: Container(
                                          width: 401,
                                          height: 1449,
                                          child: Stack(
                                            children: [
                                              Positioned(
                                                left: 9,
                                                top: 47,
                                                child: Container(
                                                  width: 384,
                                                  height: 66,
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 11,
                                                        top: -16,
                                                        child: Container(
                                                          width: 87,
                                                          height: 40,
                                                          decoration: ShapeDecoration(
                                                            color: const Color(0xFFF97316),
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(9999),
                                                            ),
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 11,
                                                                top: 5,
                                                                child: SizedBox(
                                                                  width: 65.12,
                                                                  height: 21,
                                                                  child: Text(
                                                                    'Overview',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: Colors.white,
                                                                      fontSize: 13.60,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.76,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 11,
                                                        top: 32,
                                                        child: Container(
                                                          width: 86.67,
                                                          height: 40,
                                                          decoration: ShapeDecoration(
                                                            color: const Color(0xFFFFF7ED),
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(9999),
                                                            ),
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 16,
                                                                top: 9,
                                                                child: SizedBox(
                                                                  width: 54.67,
                                                                  height: 21,
                                                                  child: Text(
                                                                    'Insights',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFEA580C),
                                                                      fontSize: 13.60,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.76,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 237,
                                                        top: 32,
                                                        child: Container(
                                                          width: 94.47,
                                                          height: 40,
                                                          decoration: ShapeDecoration(
                                                            color: const Color(0xFFFFF7ED),
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(9999),
                                                            ),
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 16,
                                                                top: 5,
                                                                child: SizedBox(
                                                                  width: 62.47,
                                                                  height: 21,
                                                                  child: Text(
                                                                    'Calendar',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFEA580C),
                                                                      fontSize: 13.60,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.76,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                left: 228,
                                                top: 31,
                                                child: Container(
                                                  width: 129.62,
                                                  height: 40,
                                                  decoration: ShapeDecoration(
                                                    color: const Color(0xFFFFF7ED),
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(9999),
                                                    ),
                                                  ),
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 16,
                                                        top: 9,
                                                        child: SizedBox(
                                                          width: 97.62,
                                                          height: 21,
                                                          child: Text(
                                                            'Daily Tracking',
                                                            textAlign: TextAlign.center,
                                                            style: TextStyle(
                                                              color: const Color(0xFFEA580C),
                                                              fontSize: 13.60,
                                                              fontFamily: 'Inter',
                                                              fontWeight: FontWeight.w400,
                                                              height: 1.76,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                left: 38,
                                                top: 145,
                                                child: Container(
                                                  width: 326,
                                                  height: 152,
                                                  decoration: ShapeDecoration(
                                                    color: const Color(0xFFFFF7ED),
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(12),
                                                    ),
                                                  ),
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 24,
                                                        top: 24,
                                                        child: Container(
                                                          width: 278,
                                                          height: 52,
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 0,
                                                                top: 0,
                                                                child: Container(
                                                                  width: 121.44,
                                                                  height: 52,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 121.44,
                                                                          height: 28,
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 0,
                                                                                child: SizedBox(
                                                                                  width: 121.44,
                                                                                  height: 27,
                                                                                  child: Text(
                                                                                    'Current Cycle',
                                                                                    style: TextStyle(
                                                                                      color: const Color(0xFF111827),
                                                                                      fontSize: 17,
                                                                                      fontFamily: 'Inter',
                                                                                      fontWeight: FontWeight.w600,
                                                                                      height: 1.65,
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 28,
                                                                        child: Container(
                                                                          width: 121.44,
                                                                          height: 24,
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 1,
                                                                                child: SizedBox(
                                                                                  width: 89.14,
                                                                                  height: 21,
                                                                                  child: Text(
                                                                                    'Day 14 of 28',
                                                                                    style: TextStyle(
                                                                                      color: const Color(0xFFEA580C),
                                                                                      fontSize: 13.60,
                                                                                      fontFamily: 'Inter',
                                                                                      fontWeight: FontWeight.w400,
                                                                                      height: 1.76,
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 242,
                                                                top: 8,
                                                                child: Container(
                                                                  width: 36,
                                                                  height: 36,
                                                                  decoration: ShapeDecoration(
                                                                    color: Colors.white,
                                                                    shape: RoundedRectangleBorder(
                                                                      borderRadius: BorderRadius.circular(9999),
                                                                    ),
                                                                  ),
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 8,
                                                                        top: 8,
                                                                        child: Container(
                                                                          width: 20,
                                                                          height: 20,
                                                                          clipBehavior: Clip.antiAlias,
                                                                          decoration: BoxDecoration(),
                                                                          child: Stack(),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 24,
                                                        top: 92,
                                                        child: Container(
                                                          width: 278,
                                                          height: 8,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(9999),
                                                            ),
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 0,
                                                                top: 0,
                                                                child: Container(
                                                                  width: 139,
                                                                  height: 8,
                                                                  decoration: ShapeDecoration(
                                                                    color: const Color(0xFFF97316),
                                                                    shape: RoundedRectangleBorder(
                                                                      borderRadius: BorderRadius.circular(9999),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 24,
                                                        top: 108,
                                                        child: Container(
                                                          width: 278,
                                                          height: 20,
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 0,
                                                                top: 0,
                                                                child: Container(
                                                                  width: 39.36,
                                                                  height: 20,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: SizedBox(
                                                                          width: 39.36,
                                                                          height: 19,
                                                                          child: Text(
                                                                            'Period',
                                                                            style: TextStyle(
                                                                              color: const Color(0xFF4B5563),
                                                                              fontSize: 11.90,
                                                                              fontFamily: 'Inter',
                                                                              fontWeight: FontWeight.w400,
                                                                              height: 1.68,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 92.61,
                                                                top: 0,
                                                                child: Container(
                                                                  width: 59.97,
                                                                  height: 20,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: SizedBox(
                                                                          width: 59.97,
                                                                          height: 19,
                                                                          child: Text(
                                                                            'Ovulation',
                                                                            style: TextStyle(
                                                                              color: const Color(0xFF4B5563),
                                                                              fontSize: 11.90,
                                                                              fontFamily: 'Inter',
                                                                              fontWeight: FontWeight.w400,
                                                                              height: 1.68,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 205.83,
                                                                top: 0,
                                                                child: Container(
                                                                  width: 72.16,
                                                                  height: 20,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: SizedBox(
                                                                          width: 72.16,
                                                                          height: 19,
                                                                          child: Text(
                                                                            'Next Period',
                                                                            style: TextStyle(
                                                                              color: const Color(0xFF4B5563),
                                                                              fontSize: 11.90,
                                                                              fontFamily: 'Inter',
                                                                              fontWeight: FontWeight.w400,
                                                                              height: 1.68,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                left: 38,
                                                top: 329,
                                                child: Container(
                                                  width: 326,
                                                  height: 80,
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 0,
                                                        top: 0,
                                                        child: Container(
                                                          width: 155,
                                                          height: 80,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(12),
                                                            ),
                                                            shadows: [
                                                              BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x0C000000),
                                                                blurRadius: 2,
                                                                offset: Offset(0, 1),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 16,
                                                                top: 22,
                                                                child: Container(
                                                                  width: 36,
                                                                  height: 36,
                                                                  decoration: ShapeDecoration(
                                                                    color: const Color(0xFFFFEDD5),
                                                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                                  ),
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 8,
                                                                        top: 8,
                                                                        child: Container(
                                                                          width: 20,
                                                                          height: 20,
                                                                          clipBehavior: Clip.antiAlias,
                                                                          decoration: BoxDecoration(),
                                                                          child: Stack(),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 64,
                                                                top: 16,
                                                                child: Container(
                                                                  width: 91.75,
                                                                  height: 48,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 32.36,
                                                                        top: 1,
                                                                        child: SizedBox(
                                                                          width: 27.02,
                                                                          height: 21,
                                                                          child: Text(
                                                                            'Log ',
                                                                            textAlign: TextAlign.center,
                                                                            style: TextStyle(
                                                                              color: const Color(0xFF1F2937),
                                                                              fontSize: 13.60,
                                                                              fontFamily: 'Inter',
                                                                              fontWeight: FontWeight.w500,
                                                                              height: 1.76,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 25,
                                                                        child: SizedBox(
                                                                          width: 91.77,
                                                                          height: 21,
                                                                          child: Text(
                                                                            'Temperature',
                                                                            textAlign: TextAlign.center,
                                                                            style: TextStyle(
                                                                              color: const Color(0xFF1F2937),
                                                                              fontSize: 13.60,
                                                                              fontFamily: 'Inter',
                                                                              fontWeight: FontWeight.w500,
                                                                              height: 1.76,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 171,
                                                        top: 0,
                                                        child: Container(
                                                          width: 155,
                                                          height: 80,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(12),
                                                            ),
                                                            shadows: [
                                                              BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x0C000000),
                                                                blurRadius: 2,
                                                                offset: Offset(0, 1),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 16,
                                                                top: 22,
                                                                child: Container(
                                                                  width: 36,
                                                                  height: 36,
                                                                  decoration: ShapeDecoration(
                                                                    color: const Color(0xFFFFEDD5),
                                                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                                  ),
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 8,
                                                                        top: 8,
                                                                        child: Container(
                                                                          width: 20,
                                                                          height: 20,
                                                                          clipBehavior: Clip.antiAlias,
                                                                          decoration: BoxDecoration(),
                                                                          child: Stack(),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 64,
                                                                top: 16,
                                                                child: Container(
                                                                  width: 75,
                                                                  height: 48,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 23.98,
                                                                        top: 1,
                                                                        child: SizedBox(
                                                                          width: 27.02,
                                                                          height: 21,
                                                                          child: Text(
                                                                            'Log ',
                                                                            textAlign: TextAlign.center,
                                                                            style: TextStyle(
                                                                              color: const Color(0xFF1F2937),
                                                                              fontSize: 13.60,
                                                                              fontFamily: 'Inter',
                                                                              fontWeight: FontWeight.w500,
                                                                              height: 1.76,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      Positioned(
                                                                        left: 11.27,
                                                                        top: 25,
                                                                        child: SizedBox(
                                                                          width: 52.47,
                                                                          height: 21,
                                                                          child: Text(
                                                                            'Weight',
                                                                            textAlign: TextAlign.center,
                                                                            style: TextStyle(
                                                                              color: const Color(0xFF1F2937),
                                                                              fontSize: 13.60,
                                                                              fontFamily: 'Inter',
                                                                              fontWeight: FontWeight.w500,
                                                                              height: 1.76,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                left: 38,
                                                top: 441,
                                                child: Container(
                                                  width: 326,
                                                  height: 656,
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 0,
                                                        top: 0,
                                                        child: Container(
                                                          width: 326,
                                                          height: 152,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(12),
                                                            ),
                                                            shadows: [
                                                              BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x0C000000),
                                                                blurRadius: 2,
                                                                offset: Offset(0, 1),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 24,
                                                                top: 24,
                                                                child: Container(
                                                                  width: 278,
                                                                  height: 104,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 48,
                                                                          height: 48,
                                                                          decoration: ShapeDecoration(
                                                                            color: const Color(0xFFFFEDD5),
                                                                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                                          ),
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 12,
                                                                                top: 12,
                                                                                child: Container(
                                                                                  width: 24,
                                                                                  height: 24,
                                                                                  clipBehavior: Clip.antiAlias,
                                                                                  decoration: BoxDecoration(),
                                                                                  child: Stack(),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      Positioned(
                                                                        left: 64,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 214,
                                                                          height: 104,
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 0,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 24,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 128.53,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'Smart Predictions',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF111827),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w600,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 32,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 72,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 205.11,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'AI-powered cycle predictions ',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 25,
                                                                                        child: SizedBox(
                                                                                          width: 194.44,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'that become more accurate ',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 49,
                                                                                        child: SizedBox(
                                                                                          width: 66.80,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'over time',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 0,
                                                        top: 176,
                                                        child: Container(
                                                          width: 326,
                                                          height: 152,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(12),
                                                            ),
                                                            shadows: [
                                                              BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x0C000000),
                                                                blurRadius: 2,
                                                                offset: Offset(0, 1),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 24,
                                                                top: 24,
                                                                child: Container(
                                                                  width: 278,
                                                                  height: 104,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 48,
                                                                          height: 48,
                                                                          decoration: ShapeDecoration(
                                                                            color: const Color(0xFFFFEDD5),
                                                                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                                          ),
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 12,
                                                                                top: 12,
                                                                                child: Container(
                                                                                  width: 24,
                                                                                  height: 24,
                                                                                  clipBehavior: Clip.antiAlias,
                                                                                  decoration: BoxDecoration(),
                                                                                  child: Stack(),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      Positioned(
                                                                        left: 64,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 214,
                                                                          height: 104,
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 0,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 24,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 134.97,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'Symptom Tracking',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF111827),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w600,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 32,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 72,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 209.81,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'Log and monitor physical and ',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 25,
                                                                                        child: SizedBox(
                                                                                          width: 148.73,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'emotional symptoms ',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 49,
                                                                                        child: SizedBox(
                                                                                          width: 156.06,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'throughout your cycle',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 0,
                                                        top: 352,
                                                        child: Container(
                                                          width: 326,
                                                          height: 152,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(12),
                                                            ),
                                                            shadows: [
                                                              BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x0C000000),
                                                                blurRadius: 2,
                                                                offset: Offset(0, 1),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 24,
                                                                top: 24,
                                                                child: Container(
                                                                  width: 278,
                                                                  height: 104,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 48,
                                                                          height: 48,
                                                                          decoration: ShapeDecoration(
                                                                            color: const Color(0xFFFFEDD5),
                                                                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                                          ),
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 12,
                                                                                top: 12,
                                                                                child: Container(
                                                                                  width: 24,
                                                                                  height: 24,
                                                                                  clipBehavior: Clip.antiAlias,
                                                                                  decoration: BoxDecoration(),
                                                                                  child: Stack(),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      Positioned(
                                                                        left: 64,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 214,
                                                                          height: 104,
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 0,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 24,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 109,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'Health Insights',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF111827),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w600,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 32,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 72,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 118.62,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'Get personalized ',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 25,
                                                                                        child: SizedBox(
                                                                                          width: 197.08,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'recommendations based on ',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 49,
                                                                                        child: SizedBox(
                                                                                          width: 133.44,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'your cycle patterns',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 0,
                                                        top: 528,
                                                        child: Container(
                                                          width: 326,
                                                          height: 128,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(12),
                                                            ),
                                                            shadows: [
                                                              BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x00000000),
                                                                blurRadius: 0,
                                                                offset: Offset(0, 0),
                                                                spreadRadius: 0,
                                                              )BoxShadow(
                                                                color: Color(0x0C000000),
                                                                blurRadius: 2,
                                                                offset: Offset(0, 1),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 24,
                                                                top: 24,
                                                                child: Container(
                                                                  width: 278,
                                                                  height: 80,
                                                                  child: Stack(
                                                                    children: [
                                                                      Positioned(
                                                                        left: 0,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 48,
                                                                          height: 48,
                                                                          decoration: ShapeDecoration(
                                                                            color: const Color(0xFFFFEDD5),
                                                                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                                          ),
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 12,
                                                                                top: 12,
                                                                                child: Container(
                                                                                  width: 24,
                                                                                  height: 24,
                                                                                  clipBehavior: Clip.antiAlias,
                                                                                  decoration: BoxDecoration(),
                                                                                  child: Stack(),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      Positioned(
                                                                        left: 64,
                                                                        top: 0,
                                                                        child: Container(
                                                                          width: 214,
                                                                          height: 80,
                                                                          child: Stack(
                                                                            children: [
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 0,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 24,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 124.59,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'Smart Reminders',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF111827),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w600,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              Positioned(
                                                                                left: 0,
                                                                                top: 32,
                                                                                child: Container(
                                                                                  width: 214,
                                                                                  height: 48,
                                                                                  child: Stack(
                                                                                    children: [
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 1,
                                                                                        child: SizedBox(
                                                                                          width: 198.42,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'Set customized notifications ',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Positioned(
                                                                                        left: 0,
                                                                                        top: 25,
                                                                                        child: SizedBox(
                                                                                          width: 183.56,
                                                                                          height: 21,
                                                                                          child: Text(
                                                                                            'for important cycle events',
                                                                                            style: TextStyle(
                                                                                              color: const Color(0xFF4B5563),
                                                                                              fontSize: 13.60,
                                                                                              fontFamily: 'Inter',
                                                                                              fontWeight: FontWeight.w400,
                                                                                              height: 1.76,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                left: 38,
                                                top: 1145,
                                                child: Container(
                                                  width: 326,
                                                  height: 256,
                                                  decoration: ShapeDecoration(
                                                    gradient: LinearGradient(
                                                      begin: Alignment(0.00, 0.50),
                                                      end: Alignment(1.00, 0.50),
                                                      colors: [const Color(0xFFF97316), const Color(0xFFEA580C)],
                                                    ),
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(12),
                                                    ),
                                                  ),
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 32,
                                                        top: 32,
                                                        child: Container(
                                                          width: 262,
                                                          height: 32,
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 15.47,
                                                                top: 0,
                                                                child: SizedBox(
                                                                  width: 231.06,
                                                                  height: 32,
                                                                  child: Text(
                                                                    'Start Tracking Today',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: Colors.white,
                                                                      fontSize: 20.40,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w700,
                                                                      height: 1.57,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 32,
                                                        top: 80,
                                                        child: Container(
                                                          width: 262,
                                                          height: 72,
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 5.09,
                                                                top: 1,
                                                                child: SizedBox(
                                                                  width: 251.80,
                                                                  height: 21,
                                                                  child: Text(
                                                                    'Join thousands of women who trust ',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFFFEDD5),
                                                                      fontSize: 13.60,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.76,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 9.05,
                                                                top: 25,
                                                                child: SizedBox(
                                                                  width: 243.91,
                                                                  height: 21,
                                                                  child: Text(
                                                                    'our cycle tracking for better health ',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFFFEDD5),
                                                                      fontSize: 13.60,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.76,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                left: 103.86,
                                                                top: 49,
                                                                child: SizedBox(
                                                                  width: 54.28,
                                                                  height: 21,
                                                                  child: Text(
                                                                    'insights',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFFFEDD5),
                                                                      fontSize: 13.60,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w400,
                                                                      height: 1.76,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        left: 70.61,
                                                        top: 176,
                                                        child: Container(
                                                          width: 184.77,
                                                          height: 48,
                                                          decoration: ShapeDecoration(
                                                            color: Colors.white,
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(9999),
                                                            ),
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                left: 32,
                                                                top: 13,
                                                                child: SizedBox(
                                                                  width: 120.77,
                                                                  height: 21,
                                                                  child: Text(
                                                                    'Get Started Now',
                                                                    textAlign: TextAlign.center,
                                                                    style: TextStyle(
                                                                      color: const Color(0xFFEA580C),
                                                                      fontSize: 13.60,
                                                                      fontFamily: 'Inter',
                                                                      fontWeight: FontWeight.w500,
                                                                      height: 1.76,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}