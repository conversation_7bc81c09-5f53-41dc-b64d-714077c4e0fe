import 'package:flutter/material.dart';

void main() {
  runApp(const FlutterApp());
}

class FlutterApp extends StatelessWidget {
  const FlutterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'HealthCare App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Inter',
        useMaterial3: true,
      ),
      home: const Splash1Screen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class Splash1Screen extends StatefulWidget {
  const Splash1Screen({super.key});

  @override
  State<Splash1Screen> createState() => _Splash1ScreenState();
}

class _Splash1ScreenState extends State<Splash1Screen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFF7ED),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: const Splash1(),
          ),
        ),
      ),
    );
  }
}

class Splash1 extends StatelessWidget {
  const Splash1({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
      child: Column(
        children: [
          // Progress indicators
          _buildProgressIndicators(),

          const SizedBox(height: 60),

          // Main content area
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Hero image with professional styling
                _buildHeroImage(),

                const SizedBox(height: 40),

                // Icon container
                _buildIconContainer(),

                const SizedBox(height: 40),

                // Title and description
                _buildContentSection(),
              ],
            ),
          ),

          const SizedBox(height: 40),

          // Navigation buttons
          _buildNavigationButtons(context),
        ],
      ),
    );
  }

  Widget _buildProgressIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildProgressDot(isActive: true),
        const SizedBox(width: 8),
        _buildProgressDot(isActive: false),
        const SizedBox(width: 8),
        _buildProgressDot(isActive: false),
      ],
    );
  }

  Widget _buildProgressDot({required bool isActive}) {
    return Container(
      width: 32,
      height: 4,
      decoration: BoxDecoration(
        color: isActive ? const Color(0xFFF97316) : const Color(0xFFFED7AA),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeroImage() {
    return Container(
      width: 280,
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0x19000000),
            blurRadius: 25,
            offset: const Offset(0, 20),
            spreadRadius: -5,
          ),
          BoxShadow(
            color: const Color(0x19000000),
            blurRadius: 10,
            offset: const Offset(0, 8),
            spreadRadius: -6,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Image.network(
          "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=400&fit=crop",
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: const Color(0xFFF3F4F6),
              child: const Icon(
                Icons.people,
                size: 80,
                color: Color(0xFF9CA3AF),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildIconContainer() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: const Color(0xFFE3D6C7),
        borderRadius: BorderRadius.circular(40),
      ),
      child: const Icon(
        Icons.people_outline,
        size: 40,
        color: Color(0xFF8B5A3C),
      ),
    );
  }

  Widget _buildContentSection() {
    return Column(
      children: [
        const Text(
          'Community Support',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Color(0xFF111827),
            fontSize: 28,
            fontWeight: FontWeight.bold,
            height: 1.2,
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Connect with healthcare workers for\npersonalized guidance',
          textAlign: TextAlign.center,
          style: TextStyle(color: Color(0xFF4B5563), fontSize: 16, height: 1.5),
        ),
      ],
    );
  }

  Widget _buildNavigationButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton(
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/completion');
          },
          child: const Text(
            'Skip',
            style: TextStyle(
              color: Color(0xFFEA580C),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        ElevatedButton.icon(
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/splash2');
          },
          icon: const Icon(Icons.arrow_forward, size: 20),
          label: const Text('Next'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFEA580C),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }
}
