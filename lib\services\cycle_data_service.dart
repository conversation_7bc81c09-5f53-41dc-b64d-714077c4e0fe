import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CycleDataService {
  static const String _cycleDataKey = 'cycle_data';
  static const String _dailyLogsKey = 'daily_logs';
  static const String _userSettingsKey = 'user_settings';

  // Singleton pattern
  static final CycleDataService _instance = CycleDataService._internal();
  factory CycleDataService() => _instance;
  CycleDataService._internal();

  // Get cycle data for calendar
  Future<Map<DateTime, String>> getCycleData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? cycleDataJson = prefs.getString(_cycleDataKey);
      
      if (cycleDataJson != null) {
        final Map<String, dynamic> data = json.decode(cycleDataJson);
        Map<DateTime, String> cycleData = {};
        
        data.forEach((key, value) {
          final DateTime date = DateTime.parse(key);
          cycleData[date] = value.toString();
        });
        
        return cycleData;
      }
    } catch (e) {
      print('Error loading cycle data: $e');
    }
    
    // Return default data if no saved data or error
    return _getDefaultCycleData();
  }

  // Save cycle data
  Future<bool> saveCycleData(Map<DateTime, String> cycleData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      Map<String, String> dataToSave = {};
      
      cycleData.forEach((key, value) {
        dataToSave[key.toIso8601String()] = value;
      });
      
      final String jsonData = json.encode(dataToSave);
      return await prefs.setString(_cycleDataKey, jsonData);
    } catch (e) {
      print('Error saving cycle data: $e');
      return false;
    }
  }

  // Get daily log for a specific date
  Future<Map<String, dynamic>?> getDailyLog(DateTime date) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? dailyLogsJson = prefs.getString(_dailyLogsKey);
      
      if (dailyLogsJson != null) {
        final Map<String, dynamic> allLogs = json.decode(dailyLogsJson);
        final String dateKey = _getDateKey(date);
        return allLogs[dateKey];
      }
    } catch (e) {
      print('Error loading daily log: $e');
    }
    return null;
  }

  // Save daily log
  Future<bool> saveDailyLog(DateTime date, Map<String, dynamic> logData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? existingLogsJson = prefs.getString(_dailyLogsKey);
      
      Map<String, dynamic> allLogs = {};
      if (existingLogsJson != null) {
        allLogs = json.decode(existingLogsJson);
      }
      
      final String dateKey = _getDateKey(date);
      allLogs[dateKey] = logData;
      
      final String jsonData = json.encode(allLogs);
      return await prefs.setString(_dailyLogsKey, jsonData);
    } catch (e) {
      print('Error saving daily log: $e');
      return false;
    }
  }

  // Get user cycle settings
  Future<Map<String, dynamic>> getUserSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? settingsJson = prefs.getString(_userSettingsKey);
      
      if (settingsJson != null) {
        return json.decode(settingsJson);
      }
    } catch (e) {
      print('Error loading user settings: $e');
    }
    
    // Return default settings
    return {
      'cycleLength': 28,
      'periodLength': 5,
      'lastPeriodStart': DateTime.now().subtract(const Duration(days: 14)).toIso8601String(),
    };
  }

  // Save user cycle settings
  Future<bool> saveUserSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String jsonData = json.encode(settings);
      return await prefs.setString(_userSettingsKey, jsonData);
    } catch (e) {
      print('Error saving user settings: $e');
      return false;
    }
  }

  // Calculate cycle predictions based on user data
  Future<Map<DateTime, String>> calculateCyclePredictions() async {
    final settings = await getUserSettings();
    final cycleLength = settings['cycleLength'] ?? 28;
    final periodLength = settings['periodLength'] ?? 5;
    final lastPeriodStartStr = settings['lastPeriodStart'];
    
    if (lastPeriodStartStr == null) return {};
    
    final lastPeriodStart = DateTime.parse(lastPeriodStartStr);
    Map<DateTime, String> predictions = {};
    
    // Calculate next 3 cycles
    for (int cycle = 0; cycle < 3; cycle++) {
      final nextPeriodStart = lastPeriodStart.add(Duration(days: cycleLength * (cycle + 1)));
      
      // Add period days
      for (int day = 0; day < periodLength; day++) {
        final periodDay = nextPeriodStart.add(Duration(days: day));
        predictions[periodDay] = cycle == 0 ? 'period' : 'predicted';
      }
      
      // Add ovulation day (typically 14 days before next period)
      final ovulationDay = nextPeriodStart.subtract(const Duration(days: 14));
      predictions[ovulationDay] = 'ovulation';
      
      // Add fertile window (5 days before ovulation to 1 day after)
      for (int day = -5; day <= 1; day++) {
        final fertileDay = ovulationDay.add(Duration(days: day));
        if (!predictions.containsKey(fertileDay)) {
          predictions[fertileDay] = 'fertile';
        }
      }
    }
    
    return predictions;
  }

  // Update period start date and recalculate predictions
  Future<bool> updatePeriodStart(DateTime newPeriodStart) async {
    final settings = await getUserSettings();
    settings['lastPeriodStart'] = newPeriodStart.toIso8601String();
    
    final success = await saveUserSettings(settings);
    if (success) {
      // Recalculate and save predictions
      final predictions = await calculateCyclePredictions();
      await saveCycleData(predictions);
    }
    
    return success;
  }

  // Mark a day as period day
  Future<bool> markPeriodDay(DateTime date, bool isPeriod) async {
    final cycleData = await getCycleData();
    
    if (isPeriod) {
      cycleData[date] = 'period';
    } else {
      cycleData.remove(date);
    }
    
    return await saveCycleData(cycleData);
  }

  // Get cycle statistics
  Future<Map<String, dynamic>> getCycleStatistics() async {
    final cycleData = await getCycleData();
    final settings = await getUserSettings();
    
    // Calculate average cycle length, period length, etc.
    List<DateTime> periodStarts = [];
    cycleData.forEach((date, type) {
      if (type == 'period') {
        // Check if this is the start of a period (no period day before it)
        final previousDay = date.subtract(const Duration(days: 1));
        if (!cycleData.containsKey(previousDay) || cycleData[previousDay] != 'period') {
          periodStarts.add(date);
        }
      }
    });
    
    int averageCycleLength = settings['cycleLength'] ?? 28;
    int averagePeriodLength = settings['periodLength'] ?? 5;
    
    if (periodStarts.length > 1) {
      // Calculate average cycle length
      List<int> cycleLengths = [];
      for (int i = 1; i < periodStarts.length; i++) {
        final length = periodStarts[i].difference(periodStarts[i - 1]).inDays;
        cycleLengths.add(length);
      }
      
      if (cycleLengths.isNotEmpty) {
        averageCycleLength = cycleLengths.reduce((a, b) => a + b) ~/ cycleLengths.length;
      }
    }
    
    return {
      'averageCycleLength': averageCycleLength,
      'averagePeriodLength': averagePeriodLength,
      'totalCyclesTracked': periodStarts.length,
      'lastPeriodStart': periodStarts.isNotEmpty ? periodStarts.last.toIso8601String() : null,
    };
  }

  // Helper methods
  String _getDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Map<DateTime, String> _getDefaultCycleData() {
    // Return sample data for demonstration
    final now = DateTime.now();
    return {
      DateTime(now.year, now.month, 1): 'period',
      DateTime(now.year, now.month, 2): 'period',
      DateTime(now.year, now.month, 3): 'period',
      DateTime(now.year, now.month, 4): 'period',
      DateTime(now.year, now.month, 5): 'period',
      DateTime(now.year, now.month, 14): 'ovulation',
      DateTime(now.year, now.month, 28): 'predicted',
      DateTime(now.year, now.month, 29): 'predicted',
      DateTime(now.year, now.month, 30): 'predicted',
    };
  }
}
