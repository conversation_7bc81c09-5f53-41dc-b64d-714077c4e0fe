import 'package:flutter/material.dart';
import 'pages/clients/loading_new.dart';
import 'pages/clients/splash_slideshow.dart';
import 'pages/clients/splash1.dart';
import 'pages/clients/splash2.dart';
import 'pages/clients/professional_signup.dart';
import 'pages/clients/professional_signin.dart';
import 'pages/clients/professional_resetpassword.dart';
import 'pages/clients/professional_email_verification.dart';
import 'pages/clients/professional_home.dart';

void main() {
  runApp(const HealthCareApp());
}

class HealthCareApp extends StatelessWidget {
  const HealthCareApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'HealthCare App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Inter',
        useMaterial3: true,
      ),
      initialRoute: '/loading',
      routes: {
        '/': (context) => const MainScreen(),
        '/loading': (context) => const LoadingScreen(),
        '/splash': (context) => const SplashSlideshowScreen(),
        '/splash1': (context) => const Splash1Screen(),
        '/splash2': (context) => const Splash2Screen(),
        '/completion': (context) => const CompletionScreen(),
        '/signup': (context) => const ProfessionalSignupScreen(),
        '/signin': (context) => const ProfessionalSigninScreen(),
        '/resetpassword': (context) => const ProfessionalResetPasswordScreen(),
        '/clienthome': (context) => const ProfessionalClientHomeScreen(),
      },
      onGenerateRoute: (settings) {
        if (settings.name == '/emailverification') {
          final args = settings.arguments as Map<String, dynamic>?;
          return MaterialPageRoute(
            builder:
                (context) => ProfessionalEmailVerificationScreen(
                  email: args?['email'] ?? '',
                ),
          );
        }
        return null;
      },
      debugShowCheckedModeBanner: false,
    );
  }
}

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('HealthCare App Demo'),
        backgroundColor: const Color(0xFFF97316),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.health_and_safety,
                size: 80,
                color: Color(0xFFF97316),
              ),
              const SizedBox(height: 24),
              const Text(
                'HealthCare App',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF111827),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Professional splash screen demo',
                style: TextStyle(fontSize: 16, color: Color(0xFF4B5563)),
              ),
              const SizedBox(height: 48),
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => Navigator.pushNamed(context, '/loading'),
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('Experience Full App Flow'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFF97316),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => Navigator.pushNamed(context, '/splash'),
                      icon: const Icon(Icons.slideshow),
                      label: const Text('View Splash Slideshow'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFFF97316),
                        side: const BorderSide(color: Color(0xFFF97316)),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: TextButton.icon(
                          onPressed:
                              () => Navigator.pushNamed(context, '/splash1'),
                          icon: const Icon(Icons.looks_one, size: 18),
                          label: const Text('Splash 1'),
                          style: TextButton.styleFrom(
                            foregroundColor: const Color(0xFF6B7280),
                          ),
                        ),
                      ),
                      Expanded(
                        child: TextButton.icon(
                          onPressed:
                              () => Navigator.pushNamed(context, '/splash2'),
                          icon: const Icon(Icons.looks_two, size: 18),
                          label: const Text('Splash 2'),
                          style: TextButton.styleFrom(
                            foregroundColor: const Color(0xFF6B7280),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Note: LoadingScreen is imported from loading_new.dart

class CompletionScreen extends StatelessWidget {
  const CompletionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0FDF4),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(Icons.check, size: 60, color: Colors.white),
              ),
              const SizedBox(height: 40),
              const Text(
                'Welcome to HealthCare!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF111827),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'You\'re all set to start your health journey with our comprehensive tracking and community support.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF4B5563),
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 48),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/signup');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF10B981),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Continue to App',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/splash1');
                },
                child: const Text(
                  'View Splash Screens Again',
                  style: TextStyle(color: Color(0xFF10B981), fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
