import 'package:flutter/material.dart';
import 'pages/clients/loadingpage.dart';
import 'pages/clients/splash1.dart';
import 'pages/clients/splash2.dart';

void main() {
  runApp(FlutterApp());
}

class FlutterApp extends StatelessWidget {
  final ValueNotifier<bool> _dark = ValueNotifier<bool>(true);
  final ValueNotifier<double> _widthFactor = ValueNotifier<double>(1.0);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      initialRoute: '/',
      routes: {
        '/': (context) => MainScreen(dark: _dark, widthFactor: _widthFactor),
        '/loading': (context) => LoadingScreen(),
        '/splash1': (context) => Splash1Screen(),
        '/splash2': (context) => Splash2Screen(),
      },
    );
  }
}

class MainScreen extends StatelessWidget {
  final ValueNotifier<bool> dark;
  final ValueNotifier<double> widthFactor;

  MainScreen({required this.dark, required this.widthFactor});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: dark,
      builder: (context, color, child) {
        return ValueListenableBuilder<double>(
          valueListenable: widthFactor,
          builder: (context, factor, child) {
            return Scaffold(
              backgroundColor: dark.value ? Colors.black : Colors.white,
              appBar: AppBar(
                title: Text('FAMARAR'),
                actions: [
                  Switch(
                    value: dark.value,
                    onChanged: (value) {
                      dark.value = value;
                    },
                  ),
                  DropdownButton<double>(
                    value: widthFactor.value,
                    onChanged: (value) {
                      widthFactor.value = value!;
                    },
                    items: [
                      DropdownMenuItem<double>(
                        value: 0.5,
                        child: Text('Size: 50%'),
                      ),
                      DropdownMenuItem<double>(
                        value: 0.75,
                        child: Text('Size: 75%'),
                      ),
                      DropdownMenuItem<double>(
                        value: 1.0,
                        child: Text('Size: 100%'),
                      ),
                    ],
                  ),
                ],
              ),
              body: Center(
                child: Container(
                  width: MediaQuery.of(context).size.width * widthFactor.value,
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      LoadingPage(),
                      SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: () => Navigator.pushNamed(context, '/loading'),
                            child: Text('Loading Page'),
                          ),
                          ElevatedButton(
                            onPressed: () => Navigator.pushNamed(context, '/splash1'),
                            child: Text('Splash 1'),
                          ),
                          ElevatedButton(
                            onPressed: () => Navigator.pushNamed(context, '/splash2'),
                            child: Text('Splash 2'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

// Wrapper classes for the screens
class LoadingScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Loading')),
      body: LoadingPage(),
    );
  }
}

class Splash1Screen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Splash 1')),
      body: Splash1(),
    );
  }
}

class Splash2Screen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Splash 2')),
      body: Splash2(),
    );
  }
}
