import 'package:flutter/material.dart';
import 'dart:async';

class SplashSlideshowScreen extends StatefulWidget {
  const SplashSlideshowScreen({super.key});

  @override
  State<SplashSlideshowScreen> createState() => _SplashSlideshowScreenState();
}

class _SplashSlideshowScreenState extends State<SplashSlideshowScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  int _currentPage = 0;
  Timer? _autoAdvanceTimer;
  
  final List<SplashData> _splashData = [
    SplashData(
      title: 'Community Support',
      description: 'Connect with healthcare workers for\npersonalized guidance',
      icon: Icons.people_outline,
      backgroundColor: const Color(0xFFFFF7ED),
      iconColor: const Color(0xFF8B5A3C),
      iconBackgroundColor: const Color(0xFFE3D6C7),
      imageUrl: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=400&fit=crop",
    ),
    SplashData(
      title: 'Health Tracking',
      description: 'Monitor your cycle and health with\nintelligent tracking',
      icon: Icons.health_and_safety_outlined,
      backgroundColor: const Color(0xFFFFEDD5),
      iconColor: const Color(0xFF8B5A3C),
      iconBackgroundColor: const Color(0xFFF4D8B2),
      imageUrl: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=400&fit=crop",
    ),
    SplashData(
      title: 'Smart Insights',
      description: 'Get AI-powered insights and\nrecommendations for better health',
      icon: Icons.psychology_outlined,
      backgroundColor: const Color(0xFFF0F9FF),
      iconColor: const Color(0xFF1E40AF),
      iconBackgroundColor: const Color(0xFFDBEAFE),
      imageUrl: "https://images.unsplash.com/photo-**********-2a8555f1a136?w=400&h=400&fit=crop",
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _fadeController.forward();
    _startAutoAdvance();
  }

  void _startAutoAdvance() {
    _autoAdvanceTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (_currentPage < _splashData.length - 1) {
        _nextPage();
      } else {
        _autoAdvanceTimer?.cancel();
        // Auto-navigate to completion after last slide
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/completion');
          }
        });
      }
    });
  }

  void _nextPage() {
    if (_currentPage < _splashData.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      Navigator.pushReplacementNamed(context, '/completion');
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipToEnd() {
    _autoAdvanceTimer?.cancel();
    Navigator.pushReplacementNamed(context, '/completion');
  }

  @override
  void dispose() {
    _autoAdvanceTimer?.cancel();
    _pageController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: PageView.builder(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentPage = index;
            });
          },
          itemCount: _splashData.length,
          itemBuilder: (context, index) {
            return _buildSplashPage(_splashData[index], index);
          },
        ),
      ),
    );
  }

  Widget _buildSplashPage(SplashData data, int index) {
    return Container(
      color: data.backgroundColor,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
          child: Column(
            children: [
              // Progress indicators
              _buildProgressIndicators(),
              
              const SizedBox(height: 60),
              
              // Main content area
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Hero image with professional styling
                    _buildHeroImage(data),
                    
                    const SizedBox(height: 40),
                    
                    // Icon container
                    _buildIconContainer(data),
                    
                    const SizedBox(height: 40),
                    
                    // Title and description
                    _buildContentSection(data),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Navigation buttons
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_splashData.length, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: _currentPage == index ? 32 : 8,
            height: 4,
            decoration: BoxDecoration(
              color: _currentPage == index 
                ? const Color(0xFFF97316) 
                : const Color(0xFFFED7AA),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildHeroImage(SplashData data) {
    return Container(
      width: 280,
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0x19000000),
            blurRadius: 25,
            offset: const Offset(0, 20),
            spreadRadius: -5,
          ),
          BoxShadow(
            color: const Color(0x19000000),
            blurRadius: 10,
            offset: const Offset(0, 8),
            spreadRadius: -6,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Image.network(
          data.imageUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: const Color(0xFFF3F4F6),
              child: Icon(
                data.icon,
                size: 80,
                color: const Color(0xFF9CA3AF),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildIconContainer(SplashData data) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: data.iconBackgroundColor,
        borderRadius: BorderRadius.circular(40),
      ),
      child: Icon(
        data.icon,
        size: 40,
        color: data.iconColor,
      ),
    );
  }

  Widget _buildContentSection(SplashData data) {
    return Column(
      children: [
        Text(
          data.title,
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: Color(0xFF111827),
            fontSize: 28,
            fontWeight: FontWeight.bold,
            height: 1.2,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          data.description,
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: Color(0xFF4B5563),
            fontSize: 16,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Previous/Skip button
        if (_currentPage > 0)
          TextButton.icon(
            onPressed: _previousPage,
            icon: const Icon(Icons.arrow_back, size: 20),
            label: const Text('Previous'),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFFEA580C),
            ),
          )
        else
          TextButton(
            onPressed: _skipToEnd,
            child: const Text(
              'Skip',
              style: TextStyle(
                color: Color(0xFFEA580C),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        
        // Next/Get Started button
        ElevatedButton.icon(
          onPressed: _currentPage < _splashData.length - 1 ? _nextPage : _skipToEnd,
          icon: Icon(
            _currentPage < _splashData.length - 1 
              ? Icons.arrow_forward 
              : Icons.check,
            size: 20,
          ),
          label: Text(
            _currentPage < _splashData.length - 1 ? 'Next' : 'Get Started',
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFEA580C),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }
}

class SplashData {
  final String title;
  final String description;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final Color iconBackgroundColor;
  final String imageUrl;

  SplashData({
    required this.title,
    required this.description,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.iconBackgroundColor,
    required this.imageUrl,
  });
}
