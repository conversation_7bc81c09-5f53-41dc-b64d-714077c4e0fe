import 'package:flutter/material.dart';

class HealthResourcesScreen extends StatefulWidget {
  const HealthResourcesScreen({super.key});

  @override
  State<HealthResourcesScreen> createState() => _HealthResourcesScreenState();
}

class _HealthResourcesScreenState extends State<HealthResourcesScreen> {
  String _selectedCategory = 'All';

  final List<String> _categories = [
    'All',
    'Articles',
    'Videos',
    'Guides',
    'Tools',
    'Support',
  ];

  final List<Map<String, dynamic>> _healthResources = [
    {
      'title': 'Understanding Your Menstrual Cycle',
      'category': 'Articles',
      'type': 'Educational Article',
      'duration': '5 min read',
      'description':
          'Comprehensive guide to understanding the phases of your menstrual cycle and what\'s normal.',
      'icon': Icons.article,
      'color': Color(0xFF7C3AED),
      'featured': true,
    },
    {
      'title': 'Reproductive Health 101',
      'category': 'Videos',
      'type': 'Video Series',
      'duration': '15 min watch',
      'description':
          'Expert-led video series covering essential reproductive health topics.',
      'icon': Icons.play_circle,
      'color': Color(0xFF059669),
      'featured': true,
    },
    {
      'title': 'Period Pain Management',
      'category': 'Guides',
      'type': 'Practical Guide',
      'duration': '8 min read',
      'description':
          'Evidence-based strategies for managing menstrual pain and discomfort.',
      'icon': Icons.healing,
      'color': Color(0xFFDC2626),
      'featured': false,
    },
    {
      'title': 'Fertility Awareness Calculator',
      'category': 'Tools',
      'type': 'Interactive Tool',
      'duration': '2 min use',
      'description':
          'Calculate your fertile window and ovulation dates with our advanced tool.',
      'icon': Icons.calculate,
      'color': Color(0xFFF97316),
      'featured': true,
    },
    {
      'title': 'PCOS: What You Need to Know',
      'category': 'Articles',
      'type': 'Medical Article',
      'duration': '10 min read',
      'description':
          'Comprehensive information about Polycystic Ovary Syndrome symptoms and management.',
      'icon': Icons.medical_information,
      'color': Color(0xFF06B6D4),
      'featured': false,
    },
    {
      'title': 'Mental Health & Hormones',
      'category': 'Articles',
      'type': 'Wellness Article',
      'duration': '7 min read',
      'description':
          'Understanding the connection between hormonal changes and mental health.',
      'icon': Icons.psychology,
      'color': Color(0xFF8B5CF6),
      'featured': false,
    },
    {
      'title': 'Nutrition for Reproductive Health',
      'category': 'Guides',
      'type': 'Nutrition Guide',
      'duration': '12 min read',
      'description':
          'Essential nutrients and dietary recommendations for optimal reproductive health.',
      'icon': Icons.restaurant,
      'color': Color(0xFF059669),
      'featured': false,
    },
    {
      'title': 'Exercise During Your Cycle',
      'category': 'Videos',
      'type': 'Workout Video',
      'duration': '20 min watch',
      'description':
          'Cycle-synced workout routines to optimize your fitness throughout the month.',
      'icon': Icons.fitness_center,
      'color': Color(0xFFEAB308),
      'featured': false,
    },
    {
      'title': 'Symptom Tracker Guide',
      'category': 'Tools',
      'type': 'User Guide',
      'duration': '5 min read',
      'description':
          'Learn how to effectively track and interpret your cycle symptoms.',
      'icon': Icons.track_changes,
      'color': Color(0xFF7C3AED),
      'featured': false,
    },
    {
      'title': 'Support Community',
      'category': 'Support',
      'type': 'Community Forum',
      'duration': 'Ongoing',
      'description':
          'Connect with others, share experiences, and get support from our community.',
      'icon': Icons.group,
      'color': Color(0xFFF97316),
      'featured': true,
    },
    {
      'title': 'Ask a Healthcare Provider',
      'category': 'Support',
      'type': 'Expert Consultation',
      'duration': '30 min session',
      'description':
          'Get personalized advice from qualified healthcare professionals.',
      'icon': Icons.medical_services,
      'color': Color(0xFF059669),
      'featured': true,
    },
    {
      'title': 'Emergency Contraception Guide',
      'category': 'Guides',
      'type': 'Emergency Guide',
      'duration': '3 min read',
      'description':
          'Important information about emergency contraception options and timing.',
      'icon': Icons.emergency,
      'color': Color(0xFFDC2626),
      'featured': false,
    },
    {
      'title': 'Understanding Contraception',
      'category': 'Videos',
      'type': 'Educational Video',
      'duration': '12 min watch',
      'description':
          'Comprehensive overview of different contraceptive methods and their effectiveness.',
      'icon': Icons.play_circle_filled,
      'color': Color(0xFF7C3AED),
      'featured': true,
      'videoUrl': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      'thumbnail': 'assets/images/contraception_thumbnail.jpg',
    },
    {
      'title': 'Menstrual Health Basics',
      'category': 'Videos',
      'type': 'Health Education',
      'duration': '8 min watch',
      'description':
          'Essential information about menstrual health, hygiene, and common concerns.',
      'icon': Icons.video_library,
      'color': Color(0xFFDC2626),
      'featured': false,
      'videoUrl': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      'thumbnail': 'assets/images/menstrual_thumbnail.jpg',
    },
    {
      'title': 'Fertility Awareness Method',
      'category': 'Videos',
      'type': 'Tutorial Video',
      'duration': '15 min watch',
      'description':
          'Learn how to track your fertility signs and understand your reproductive cycle.',
      'icon': Icons.play_arrow,
      'color': Color(0xFF059669),
      'featured': true,
      'videoUrl': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      'thumbnail': 'assets/images/fertility_thumbnail.jpg',
    },
    {
      'title': 'Hormonal Changes Explained',
      'category': 'Videos',
      'type': 'Medical Video',
      'duration': '18 min watch',
      'description':
          'Expert explanation of hormonal changes throughout the menstrual cycle.',
      'icon': Icons.science,
      'color': Color(0xFF8B5CF6),
      'featured': false,
      'videoUrl': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      'thumbnail': 'assets/images/hormones_thumbnail.jpg',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      body: Column(
        children: [
          // Header Section
          _buildHeader(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Featured Resources Section
                  _buildFeaturedSection(),

                  // Category Filter
                  _buildCategoryFilter(),

                  // Resources Grid
                  _buildResourcesGrid(),

                  // Quick Access Section
                  _buildQuickAccessSection(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFF97316), Color(0xFFEA580C)],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                  ),
                  const Expanded(
                    child: Text(
                      'Health Resources',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.library_books,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              const Text(
                'Your Health\nEducation Hub',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'Access expert-curated articles, videos,\ntools, and support to empower your\nreproductive health journey',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedSection() {
    List<Map<String, dynamic>> featuredResources =
        _healthResources
            .where((resource) => resource['featured'] == true)
            .take(3)
            .toList();

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Featured Resources',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: featuredResources.length,
              itemBuilder: (context, index) {
                return _buildFeaturedCard(featuredResources[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedCard(Map<String, dynamic> resource) {
    return GestureDetector(
      onTap: () {
        if (resource['category'] == 'Videos') {
          _showVideoPlayer(resource);
        } else {
          _showResourceDetails(resource);
        }
      },
      child: Container(
        width: 280,
        margin: const EdgeInsets.only(right: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              resource['color'],
              resource['color'].withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: resource['color'].withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(resource['icon'], color: Colors.white, size: 20),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    resource['type'],
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              resource['title'],
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Flexible(
              child: Text(
                resource['description'],
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  height: 1.3,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: Colors.white.withValues(alpha: 0.8),
                  size: 14,
                ),
                const SizedBox(width: 4),
                Text(
                  resource['duration'],
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                const Spacer(),
                const Icon(Icons.arrow_forward, color: Colors.white, size: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children:
              _categories.map((category) {
                bool isSelected = _selectedCategory == category;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? const Color(0xFFF97316) : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? const Color(0xFFF97316)
                                : const Color(0xFFE5E7EB),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      category,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color:
                            isSelected ? Colors.white : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildResourcesGrid() {
    List<Map<String, dynamic>> filteredResources =
        _selectedCategory == 'All'
            ? _healthResources
            : _healthResources
                .where((resource) => resource['category'] == _selectedCategory)
                .toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _selectedCategory == 'All'
                ? 'All Resources'
                : '$_selectedCategory Resources',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          ...filteredResources.map((resource) => _buildResourceCard(resource)),
        ],
      ),
    );
  }

  Widget _buildResourceCard(Map<String, dynamic> resource) {
    return GestureDetector(
      onTap: () {
        if (resource['category'] == 'Videos') {
          _showVideoPlayer(resource);
        } else {
          _showResourceDetails(resource);
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: resource['color'].withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                children: [
                  Icon(resource['icon'], color: resource['color'], size: 24),
                  if (resource['category'] == 'Videos')
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: resource['color'],
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 8,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          resource['title'],
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF111827),
                          ),
                        ),
                      ),
                      if (resource['featured'])
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF97316),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            'Featured',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    resource['description'],
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF6B7280),
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: resource['color'].withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          resource['type'],
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: resource['color'],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.access_time,
                        color: Color(0xFF6B7280),
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        resource['duration'],
                        style: const TextStyle(
                          fontSize: 10,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF6B7280),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showVideoPlayer(Map<String, dynamic> resource) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.7,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // Video Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: resource['color'],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            resource['title'],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Video Player Area
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: const BoxDecoration(color: Colors.black87),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.play_circle_filled,
                            size: 80,
                            color: resource['color'],
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Video Player',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            resource['description'],
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () {
                              // Here you would integrate with a video player
                              // For now, show a placeholder message
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Playing: ${resource['title']}',
                                  ),
                                  backgroundColor: resource['color'],
                                ),
                              );
                            },
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('Play Video'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: resource['color'],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Video Info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(16),
                        bottomRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: resource['color'].withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            resource['type'],
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: resource['color'],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.access_time,
                          color: const Color(0xFF6B7280),
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          resource['duration'],
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF6B7280),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showResourceDetails(Map<String, dynamic> resource) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: resource['color'].withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Icon(
                                resource['icon'],
                                color: resource['color'],
                                size: 32,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    resource['title'],
                                    style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF111827),
                                    ),
                                  ),
                                  Text(
                                    '${resource['type']} • ${resource['duration']}',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF6B7280),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        Text(
                          resource['description'],
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xFF6B7280),
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 24),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: resource['color'].withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'About this ${resource['category'].toLowerCase().substring(0, resource['category'].length - 1)}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: resource['color'],
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'This resource has been reviewed by certified healthcare professionals and provides evidence-based information to support your reproductive health journey.',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF6B7280),
                                  height: 1.4,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildQuickAccessSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Access',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickAccessItem(
                  'Emergency\nContacts',
                  Icons.emergency,
                  const Color(0xFFDC2626),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickAccessItem(
                  'Find\nClinics',
                  Icons.location_on,
                  const Color(0xFF059669),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickAccessItem(
                  'Health\nHotline',
                  Icons.phone,
                  const Color(0xFF7C3AED),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF7ED),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Row(
              children: [
                Icon(Icons.info, color: Color(0xFFF97316), size: 20),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'All health information is reviewed by certified healthcare professionals',
                    style: TextStyle(fontSize: 12, color: Color(0xFF6B7280)),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessItem(String title, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        _showQuickAccessDialog(title);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.white, size: 20),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
                height: 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showQuickAccessDialog(String title) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text('This feature will connect you to $title services.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFF97316),
                ),
                child: const Text(
                  'Access',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }
}
