import 'package:flutter/material.dart';

class HealthResourcesScreen extends StatefulWidget {
  const HealthResourcesScreen({super.key});

  @override
  State<HealthResourcesScreen> createState() => _HealthResourcesScreenState();
}

class _HealthResourcesScreenState extends State<HealthResourcesScreen> {
  String _selectedCategory = 'All';

  final List<String> _categories = [
    'All',
    'Articles',
    'Videos',
    'Guides',
    'Tools',
    'Support',
  ];

  final List<Map<String, dynamic>> _healthResources = [
    {
      'title': 'Understanding Your Menstrual Cycle',
      'category': 'Articles',
      'type': 'Educational Article',
      'duration': '5 min read',
      'description':
          'Comprehensive guide to understanding the phases of your menstrual cycle and what\'s normal.',
      'icon': Icons.article,
      'color': Color(0xFF7C3AED),
      'featured': true,
    },
    {
      'title': 'Reproductive Health 101',
      'category': 'Videos',
      'type': 'Video Series',
      'duration': '15 min watch',
      'description':
          'Expert-led video series covering essential reproductive health topics.',
      'icon': Icons.play_circle,
      'color': Color(0xFF059669),
      'featured': true,
    },
    {
      'title': 'Period Pain Management',
      'category': 'Guides',
      'type': 'Practical Guide',
      'duration': '8 min read',
      'description':
          'Evidence-based strategies for managing menstrual pain and discomfort.',
      'icon': Icons.healing,
      'color': Color(0xFFDC2626),
      'featured': false,
    },
    {
      'title': 'Fertility Awareness Calculator',
      'category': 'Tools',
      'type': 'Interactive Tool',
      'duration': '2 min use',
      'description':
          'Calculate your fertile window and ovulation dates with our advanced tool.',
      'icon': Icons.calculate,
      'color': Color(0xFFF97316),
      'featured': true,
    },
    {
      'title': 'PCOS: What You Need to Know',
      'category': 'Articles',
      'type': 'Medical Article',
      'duration': '10 min read',
      'description':
          'Comprehensive information about Polycystic Ovary Syndrome symptoms and management.',
      'icon': Icons.medical_information,
      'color': Color(0xFF06B6D4),
      'featured': false,
    },
    {
      'title': 'Mental Health & Hormones',
      'category': 'Articles',
      'type': 'Wellness Article',
      'duration': '7 min read',
      'description':
          'Understanding the connection between hormonal changes and mental health.',
      'icon': Icons.psychology,
      'color': Color(0xFF8B5CF6),
      'featured': false,
    },
    {
      'title': 'Nutrition for Reproductive Health',
      'category': 'Guides',
      'type': 'Nutrition Guide',
      'duration': '12 min read',
      'description':
          'Essential nutrients and dietary recommendations for optimal reproductive health.',
      'icon': Icons.restaurant,
      'color': Color(0xFF059669),
      'featured': false,
    },
    {
      'title': 'Exercise During Your Cycle',
      'category': 'Videos',
      'type': 'Workout Video',
      'duration': '20 min watch',
      'description':
          'Cycle-synced workout routines to optimize your fitness throughout the month.',
      'icon': Icons.fitness_center,
      'color': Color(0xFFEAB308),
      'featured': false,
    },
    {
      'title': 'Symptom Tracker Guide',
      'category': 'Tools',
      'type': 'User Guide',
      'duration': '5 min read',
      'description':
          'Learn how to effectively track and interpret your cycle symptoms.',
      'icon': Icons.track_changes,
      'color': Color(0xFF7C3AED),
      'featured': false,
    },
    {
      'title': 'Support Community',
      'category': 'Support',
      'type': 'Community Forum',
      'duration': 'Ongoing',
      'description':
          'Connect with others, share experiences, and get support from our community.',
      'icon': Icons.group,
      'color': Color(0xFFF97316),
      'featured': true,
    },
    {
      'title': 'Ask a Healthcare Provider',
      'category': 'Support',
      'type': 'Expert Consultation',
      'duration': '30 min session',
      'description':
          'Get personalized advice from qualified healthcare professionals.',
      'icon': Icons.medical_services,
      'color': Color(0xFF059669),
      'featured': true,
    },
    {
      'title': 'Emergency Contraception Guide',
      'category': 'Guides',
      'type': 'Emergency Guide',
      'duration': '3 min read',
      'description':
          'Important information about emergency contraception options and timing.',
      'icon': Icons.emergency,
      'color': Color(0xFFDC2626),
      'featured': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      body: Column(
        children: [
          // Header Section
          _buildHeader(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Featured Resources Section
                  _buildFeaturedSection(),

                  // Category Filter
                  _buildCategoryFilter(),

                  // Resources Grid
                  _buildResourcesGrid(),

                  // Quick Access Section
                  _buildQuickAccessSection(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFF97316), Color(0xFFEA580C)],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                  ),
                  const Expanded(
                    child: Text(
                      'Health Resources',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.library_books,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              const Text(
                'Your Health\nEducation Hub',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'Access expert-curated articles, videos,\ntools, and support to empower your\nreproductive health journey',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedSection() {
    List<Map<String, dynamic>> featuredResources =
        _healthResources
            .where((resource) => resource['featured'] == true)
            .take(3)
            .toList();

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Featured Resources',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: featuredResources.length,
              itemBuilder: (context, index) {
                return _buildFeaturedCard(featuredResources[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedCard(Map<String, dynamic> resource) {
    return Container(
      width: 280,
      margin: const EdgeInsets.only(right: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [resource['color'], resource['color'].withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: resource['color'].withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(resource['icon'], color: Colors.white, size: 20),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  resource['type'],
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            resource['title'],
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            resource['description'],
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const Spacer(),
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: Colors.white.withValues(alpha: 0.8),
                size: 14,
              ),
              const SizedBox(width: 4),
              Text(
                resource['duration'],
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.8),
                ),
              ),
              const Spacer(),
              const Icon(Icons.arrow_forward, color: Colors.white, size: 16),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children:
              _categories.map((category) {
                bool isSelected = _selectedCategory == category;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? const Color(0xFFF97316) : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? const Color(0xFFF97316)
                                : const Color(0xFFE5E7EB),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      category,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color:
                            isSelected ? Colors.white : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildResourcesGrid() {
    List<Map<String, dynamic>> filteredResources =
        _selectedCategory == 'All'
            ? _healthResources
            : _healthResources
                .where((resource) => resource['category'] == _selectedCategory)
                .toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _selectedCategory == 'All'
                ? 'All Resources'
                : '$_selectedCategory Resources',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          ...filteredResources.map((resource) => _buildResourceCard(resource)),
        ],
      ),
    );
  }

  Widget _buildResourceCard(Map<String, dynamic> resource) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: resource['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(resource['icon'], color: resource['color'], size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        resource['title'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF111827),
                        ),
                      ),
                    ),
                    if (resource['featured'])
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF97316),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'Featured',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  resource['description'],
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: resource['color'].withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        resource['type'],
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: resource['color'],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Icon(
                      Icons.access_time,
                      color: Color(0xFF6B7280),
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      resource['duration'],
                      style: const TextStyle(
                        fontSize: 10,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          const Icon(
            Icons.arrow_forward_ios,
            color: Color(0xFF6B7280),
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Access',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickAccessItem(
                  'Emergency\nContacts',
                  Icons.emergency,
                  const Color(0xFFDC2626),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickAccessItem(
                  'Find\nClinics',
                  Icons.location_on,
                  const Color(0xFF059669),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickAccessItem(
                  'Health\nHotline',
                  Icons.phone,
                  const Color(0xFF7C3AED),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF7ED),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Row(
              children: [
                Icon(Icons.info, color: Color(0xFFF97316), size: 20),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'All health information is reviewed by certified healthcare professionals',
                    style: TextStyle(fontSize: 12, color: Color(0xFF6B7280)),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessItem(String title, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        _showQuickAccessDialog(title);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.white, size: 20),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
                height: 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showQuickAccessDialog(String title) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text('This feature will connect you to $title services.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFF97316),
                ),
                child: const Text(
                  'Access',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }
}
