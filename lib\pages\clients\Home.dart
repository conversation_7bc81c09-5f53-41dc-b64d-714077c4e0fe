import 'package:flutter/material.dart';

void main() {
  runApp(const FigmaToCodeApp());
}

// Generated by: https://www.figma.com/community/plugin/842128343887142055/
class FigmaToCodeApp extends StatelessWidget {
  const FigmaToCodeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData.dark().copyWith(
        scaffoldBackgroundColor: const Color.fromARGB(255, 18, 32, 47),
      ),
      home: Scaffold(
        body: ListView(children: [
          Home(),
        ]),
      ),
    );
  }
}

class Home extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 402,
          height: 2185,
          decoration: BoxDecoration(color: Colors.white),
          child: Stack(
            children: [
              Positioned(
                left: 0,
                top: -0.24,
                child: Container(
                  width: 402,
                  height: 503,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(-0.50, 0.00),
                      end: Alignment(-0.50, 1.00),
                      colors: [const Color(0xFFFFF7ED), Colors.white],
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        left: 37,
                        top: 48,
                        child: Container(
                          width: 328,
                          height: 180,
                          child: Stack(
                            children: [
                              Positioned(
                                left: 0,
                                top: 0,
                                child: Container(
                                  width: 328,
                                  height: 40,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 104.84,
                                        top: -4,
                                        child: SizedBox(
                                          width: 118.30,
                                          height: 48,
                                          child: Text(
                                            'ComFP',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFF111827),
                                              fontSize: 30.60,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w700,
                                              height: 1.31,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 0,
                                top: 52,
                                child: Container(
                                  width: 328,
                                  height: 56,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 46.17,
                                        top: 2,
                                        child: SizedBox(
                                          width: 235.64,
                                          height: 24,
                                          child: Text(
                                            'Your personal family planning ',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFF4B5563),
                                              fontSize: 15.30,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              height: 1.83,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 119.30,
                                        top: 30,
                                        child: SizedBox(
                                          width: 89.39,
                                          height: 24,
                                          child: Text(
                                            'companion',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFF4B5563),
                                              fontSize: 15.30,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              height: 1.83,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        left: 37,
                        top: 260,
                        child: Container(
                          width: 328,
                          height: 218.66,
                          child: Stack(
                            children: [
                              Positioned(
                                left: 0,
                                top: 0,
                                child: Container(
                                  width: 328,
                                  height: 218.66,
                                  decoration: ShapeDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage("https://placehold.co/328x219"),
                                      fit: BoxFit.cover,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    shadows: [
                                      BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x3F000000),
                                        blurRadius: 50,
                                        offset: Offset(0, 25),
                                        spreadRadius: -12,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 124,
                                top: 210.66,
                                child: Container(
                                  width: 80,
                                  height: 24,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(9999),
                                    ),
                                    shadows: [
                                      BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x19000000),
                                        blurRadius: 15,
                                        offset: Offset(0, 10),
                                        spreadRadius: -3,
                                      )BoxShadow(
                                        color: Color(0x19000000),
                                        blurRadius: 6,
                                        offset: Offset(0, 4),
                                        spreadRadius: -4,
                                      )
                                    ],
                                  ),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 24,
                                        top: 8,
                                        child: Container(
                                          width: 32,
                                          height: 8,
                                          child: Stack(
                                            children: [
                                              Positioned(
                                                left: 0,
                                                top: 0,
                                                child: Container(
                                                  width: 8,
                                                  height: 8,
                                                  decoration: ShapeDecoration(
                                                    color: const Color(0xFFF97316),
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(9999),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                left: 12,
                                                top: 0,
                                                child: Container(
                                                  width: 8,
                                                  height: 8,
                                                  decoration: ShapeDecoration(
                                                    color: const Color(0xFFFDBA74),
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(9999),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                left: 24,
                                                top: 0,
                                                child: Container(
                                                  width: 8,
                                                  height: 8,
                                                  decoration: ShapeDecoration(
                                                    color: const Color(0xFFFED7AA),
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(9999),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 502.76,
                child: Container(
                  width: 402,
                  height: 768,
                  child: Stack(
                    children: [
                      Positioned(
                        left: 37,
                        top: 48,
                        child: Container(
                          width: 328,
                          height: 64,
                          child: Stack(
                            children: [
                              Positioned(
                                left: 27.75,
                                top: 0,
                                child: SizedBox(
                                  width: 272.50,
                                  height: 32,
                                  child: Text(
                                    'Everything you need for ',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: const Color(0xFF111827),
                                      fontSize: 20.40,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w700,
                                      height: 1.57,
                                    ),
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 75.42,
                                top: 32,
                                child: SizedBox(
                                  width: 177.14,
                                  height: 32,
                                  child: Text(
                                    'family planning',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: const Color(0xFF111827),
                                      fontSize: 20.40,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w700,
                                      height: 1.57,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        left: 34,
                        top: 143.24,
                        child: Container(
                          width: 329,
                          height: 96,
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0),
                          ),
                        ),
                      ),
                      Positioned(
                        left: 34,
                        top: 143.24,
                        child: Container(
                          width: 332,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            spacing: 19,
                            children: [
                              Container(
                                width: double.infinity,
                                height: 96,
                                decoration: ShapeDecoration(
                                  color: const Color(0xFFFFF7ED),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      left: 16,
                                      top: 16,
                                      child: Container(
                                        width: 48,
                                        height: 48,
                                        decoration: ShapeDecoration(
                                          color: const Color(0xFFFFEDD5),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                        ),
                                        child: Stack(
                                          children: [
                                            Positioned(
                                              left: 12,
                                              top: 12,
                                              child: Container(
                                                width: 24,
                                                height: 24,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(),
                                                child: Stack(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      left: 80,
                                      top: 16,
                                      child: Container(
                                        width: 196,
                                        height: 64,
                                        child: Stack(
                                          children: [
                                            Positioned(
                                              left: 0,
                                              top: 0,
                                              child: Container(
                                                width: 196,
                                                height: 24,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 0,
                                                      top: 1,
                                                      child: SizedBox(
                                                        width: 169.88,
                                                        height: 21,
                                                        child: Text(
                                                          'Cycle Tracking',
                                                          style: TextStyle(
                                                            color: const Color(0xFF111827),
                                                            fontSize: 13.60,
                                                            fontFamily: 'Inter',
                                                            fontWeight: FontWeight.w600,
                                                            height: 1.76,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 0,
                                              top: 24,
                                              child: Container(
                                                width: 196,
                                                height: 40,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 0,
                                                      top: 0,
                                                      child: SizedBox(
                                                        width: 187,
                                                        height: 39,
                                                        child: Text(
                                                          'Monitor your menstrual cycle \nwith accurate predictions.',
                                                          style: TextStyle(
                                                            color: const Color(0xFF4B5563),
                                                            fontSize: 11.90,
                                                            fontFamily: 'Inter',
                                                            fontWeight: FontWeight.w400,
                                                            height: 1.68,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      left: 292,
                                      top: 16,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(),
                                        child: Stack(),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                height: 96,
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      left: 0,
                                      top: 0,
                                      child: Container(
                                        width: 328,
                                        height: 96,
                                        decoration: ShapeDecoration(
                                          color: const Color(0xFFFFF7ED),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                        ),
                                        child: Stack(
                                          children: [
                                            Positioned(
                                              left: 16,
                                              top: 16,
                                              child: Container(
                                                width: 48,
                                                height: 48,
                                                decoration: ShapeDecoration(
                                                  color: const Color(0xFFFFEDD5),
                                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                ),
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 12,
                                                      top: 12,
                                                      child: Container(
                                                        width: 24,
                                                        height: 24,
                                                        clipBehavior: Clip.antiAlias,
                                                        decoration: BoxDecoration(),
                                                        child: Stack(),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 80,
                                              top: 16,
                                              child: Container(
                                                width: 196,
                                                height: 64,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 0,
                                                      top: 0,
                                                      child: Container(
                                                        width: 196,
                                                        height: 24,
                                                        child: Stack(
                                                          children: [
                                                            Positioned(
                                                              left: 0,
                                                              top: 1,
                                                              child: SizedBox(
                                                                width: 169.88,
                                                                height: 21,
                                                                child: Text(
                                                                  'Contraceptive Methods',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF111827),
                                                                    fontSize: 13.60,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w600,
                                                                    height: 1.76,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      left: 0,
                                                      top: 24,
                                                      child: Container(
                                                        width: 196,
                                                        height: 40,
                                                        child: Stack(
                                                          children: [
                                                            Positioned(
                                                              left: 0,
                                                              top: 0,
                                                              child: SizedBox(
                                                                width: 187.17,
                                                                height: 19,
                                                                child: Text(
                                                                  'Explore and compare different ',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF4B5563),
                                                                    fontSize: 11.90,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w400,
                                                                    height: 1.68,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            Positioned(
                                                              left: 0,
                                                              top: 20,
                                                              child: SizedBox(
                                                                width: 133.98,
                                                                height: 19,
                                                                child: Text(
                                                                  'contraceptive options',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF4B5563),
                                                                    fontSize: 11.90,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w400,
                                                                    height: 1.68,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 292,
                                              top: 16,
                                              child: Container(
                                                width: 20,
                                                height: 20,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(),
                                                child: Stack(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                height: 96,
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      left: 0,
                                      top: 0,
                                      child: Container(
                                        width: 328,
                                        height: 96,
                                        decoration: ShapeDecoration(
                                          color: const Color(0xFFFFF7ED),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                        ),
                                        child: Stack(
                                          children: [
                                            Positioned(
                                              left: 16,
                                              top: 16,
                                              child: Container(
                                                width: 48,
                                                height: 48,
                                                decoration: ShapeDecoration(
                                                  color: const Color(0xFFFFEDD5),
                                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                ),
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 12,
                                                      top: 12,
                                                      child: Container(
                                                        width: 24,
                                                        height: 24,
                                                        clipBehavior: Clip.antiAlias,
                                                        decoration: BoxDecoration(),
                                                        child: Stack(),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 80,
                                              top: 16,
                                              child: Container(
                                                width: 196,
                                                height: 64,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 0,
                                                      top: 0,
                                                      child: Container(
                                                        width: 196,
                                                        height: 24,
                                                        child: Stack(
                                                          children: [
                                                            Positioned(
                                                              left: 0,
                                                              top: 1,
                                                              child: SizedBox(
                                                                width: 124.83,
                                                                height: 21,
                                                                child: Text(
                                                                  'Health Resources',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF111827),
                                                                    fontSize: 13.60,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w600,
                                                                    height: 1.76,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      left: 0,
                                                      top: 24,
                                                      child: Container(
                                                        width: 196,
                                                        height: 40,
                                                        child: Stack(
                                                          children: [
                                                            Positioned(
                                                              left: 0,
                                                              top: 0,
                                                              child: SizedBox(
                                                                width: 179.64,
                                                                height: 19,
                                                                child: Text(
                                                                  'Access comprehensive family ',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF4B5563),
                                                                    fontSize: 11.90,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w400,
                                                                    height: 1.68,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            Positioned(
                                                              left: 0,
                                                              top: 20,
                                                              child: SizedBox(
                                                                width: 130.20,
                                                                height: 19,
                                                                child: Text(
                                                                  'planning information',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF4B5563),
                                                                    fontSize: 11.90,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w400,
                                                                    height: 1.68,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 292,
                                              top: 16,
                                              child: Container(
                                                width: 20,
                                                height: 20,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(),
                                                child: Stack(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                height: 96,
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      left: 0,
                                      top: 0,
                                      child: Container(
                                        width: 328,
                                        height: 96,
                                        decoration: ShapeDecoration(
                                          color: const Color(0xFFFFF7ED),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                        ),
                                        child: Stack(
                                          children: [
                                            Positioned(
                                              left: 16,
                                              top: 16,
                                              child: Container(
                                                width: 48,
                                                height: 48,
                                                decoration: ShapeDecoration(
                                                  color: const Color(0xFFFFEDD5),
                                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                ),
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 12,
                                                      top: 12,
                                                      child: Container(
                                                        width: 24,
                                                        height: 24,
                                                        clipBehavior: Clip.antiAlias,
                                                        decoration: BoxDecoration(),
                                                        child: Stack(),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 80,
                                              top: 16,
                                              child: Container(
                                                width: 196,
                                                height: 64,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 0,
                                                      top: 0,
                                                      child: Container(
                                                        width: 196,
                                                        height: 24,
                                                        child: Stack(
                                                          children: [
                                                            Positioned(
                                                              left: 0,
                                                              top: 1,
                                                              child: SizedBox(
                                                                width: 110.05,
                                                                height: 21,
                                                                child: Text(
                                                                  'Expert Support',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF111827),
                                                                    fontSize: 13.60,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w600,
                                                                    height: 1.76,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      left: 0,
                                                      top: 24,
                                                      child: Container(
                                                        width: 196,
                                                        height: 40,
                                                        child: Stack(
                                                          children: [
                                                            Positioned(
                                                              left: 0,
                                                              top: 0,
                                                              child: SizedBox(
                                                                width: 154.64,
                                                                height: 19,
                                                                child: Text(
                                                                  'Connect with community ',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF4B5563),
                                                                    fontSize: 11.90,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w400,
                                                                    height: 1.68,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            Positioned(
                                                              left: 0,
                                                              top: 20,
                                                              child: SizedBox(
                                                                width: 143.72,
                                                                height: 19,
                                                                child: Text(
                                                                  'health workers anytime',
                                                                  style: TextStyle(
                                                                    color: const Color(0xFF4B5563),
                                                                    fontSize: 11.90,
                                                                    fontFamily: 'Inter',
                                                                    fontWeight: FontWeight.w400,
                                                                    height: 1.68,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 292,
                                              top: 16,
                                              child: Container(
                                                width: 20,
                                                height: 20,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(),
                                                child: Stack(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                height: 96,
                                decoration: ShapeDecoration(
                                  color: const Color(0xFFFFF7ED),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      left: 16,
                                      top: 16,
                                      child: Container(
                                        width: 48,
                                        height: 48,
                                        decoration: ShapeDecoration(
                                          color: const Color(0xFFFFEDD5),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                        ),
                                        child: Stack(
                                          children: [
                                            Positioned(
                                              left: 12,
                                              top: 12,
                                              child: Container(
                                                width: 24,
                                                height: 24,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(),
                                                child: Stack(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      left: 80,
                                      top: 16,
                                      child: Container(
                                        width: 196,
                                        height: 64,
                                        child: Stack(
                                          children: [
                                            Positioned(
                                              left: 0,
                                              top: 0,
                                              child: Container(
                                                width: 196,
                                                height: 24,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 0,
                                                      top: 1,
                                                      child: SizedBox(
                                                        width: 118.44,
                                                        height: 21,
                                                        child: Text(
                                                          'Private & Secure',
                                                          style: TextStyle(
                                                            color: const Color(0xFF111827),
                                                            fontSize: 13.60,
                                                            fontFamily: 'Inter',
                                                            fontWeight: FontWeight.w600,
                                                            height: 1.76,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 0,
                                              top: 24,
                                              child: Container(
                                                width: 196,
                                                height: 40,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      left: 0,
                                                      top: 0,
                                                      child: SizedBox(
                                                        width: 162.84,
                                                        height: 19,
                                                        child: Text(
                                                          'Your data is protected and ',
                                                          style: TextStyle(
                                                            color: const Color(0xFF4B5563),
                                                            fontSize: 11.90,
                                                            fontFamily: 'Inter',
                                                            fontWeight: FontWeight.w400,
                                                            height: 1.68,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      left: 0,
                                                      top: 20,
                                                      child: SizedBox(
                                                        width: 72.52,
                                                        height: 19,
                                                        child: Text(
                                                          'confidential',
                                                          style: TextStyle(
                                                            color: const Color(0xFF4B5563),
                                                            fontSize: 11.90,
                                                            fontFamily: 'Inter',
                                                            fontWeight: FontWeight.w400,
                                                            height: 1.68,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      left: 292,
                                      top: 16,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(),
                                        child: Stack(),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 1270.76,
                child: Container(
                  width: 402,
                  height: 485,
                  decoration: BoxDecoration(color: const Color(0xFFFFF7ED)),
                  child: Stack(
                    children: [
                      Positioned(
                        left: 37,
                        top: 48,
                        child: Container(
                          width: 328,
                          height: 124,
                          child: Stack(
                            children: [
                              Positioned(
                                left: 0,
                                top: 0,
                                child: Container(
                                  width: 328,
                                  height: 64,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 17.73,
                                        top: 0,
                                        child: SizedBox(
                                          width: 292.52,
                                          height: 32,
                                          child: Text(
                                            'Supported by Community ',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFF111827),
                                              fontSize: 20.40,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w700,
                                              height: 1.57,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 76.23,
                                        top: 32,
                                        child: SizedBox(
                                          width: 175.53,
                                          height: 32,
                                          child: Text(
                                            'Health Workers',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFF111827),
                                              fontSize: 20.40,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w700,
                                              height: 1.57,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 0,
                                top: 76,
                                child: Container(
                                  width: 328,
                                  height: 48,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 23.97,
                                        top: 1,
                                        child: SizedBox(
                                          width: 280.06,
                                          height: 21,
                                          child: Text(
                                            'Get personalized guidance from trained ',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFF4B5563),
                                              fontSize: 13.60,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              height: 1.76,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 117.80,
                                        top: 25,
                                        child: SizedBox(
                                          width: 92.41,
                                          height: 21,
                                          child: Text(
                                            'professionals',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFF4B5563),
                                              fontSize: 13.60,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              height: 1.76,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        left: 37,
                        top: 204,
                        child: Container(
                          width: 328,
                          height: 200.86,
                          child: Stack(
                            children: [
                              Positioned(
                                left: 0,
                                top: 0,
                                child: Container(
                                  width: 328,
                                  height: 200.86,
                                  decoration: ShapeDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage("https://placehold.co/328x201"),
                                      fit: BoxFit.cover,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    shadows: [
                                      BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x00000000),
                                        blurRadius: 0,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )BoxShadow(
                                        color: Color(0x19000000),
                                        blurRadius: 15,
                                        offset: Offset(0, 10),
                                        spreadRadius: -3,
                                      )BoxShadow(
                                        color: Color(0x19000000),
                                        blurRadius: 6,
                                        offset: Offset(0, 4),
                                        spreadRadius: -4,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 16,
                                top: 132.86,
                                child: Container(
                                  width: 296,
                                  height: 52,
                                  decoration: ShapeDecoration(
                                    color: Colors.white.withValues(alpha: 229),
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                  ),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 16,
                                        top: 16,
                                        child: Container(
                                          width: 264,
                                          height: 20,
                                          child: Stack(
                                            children: [
                                              Positioned(
                                                left: 0,
                                                top: 0,
                                                child: Container(
                                                  width: 20,
                                                  height: 20,
                                                  clipBehavior: Clip.antiAlias,
                                                  decoration: BoxDecoration(),
                                                  child: Stack(),
                                                ),
                                              ),
                                              Positioned(
                                                left: 32,
                                                top: 0,
                                                child: Container(
                                                  width: 165.36,
                                                  height: 20,
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        left: 0,
                                                        top: 0,
                                                        child: SizedBox(
                                                          width: 165.36,
                                                          height: 19,
                                                          child: Text(
                                                            '24/7 Professional Support',
                                                            style: TextStyle(
                                                              color: const Color(0xFF111827),
                                                              fontSize: 11.90,
                                                              fontFamily: 'Inter',
                                                              fontWeight: FontWeight.w500,
                                                              height: 1.68,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 1755.76,
                child: Container(
                  width: 402,
                  height: 308,
                  child: Stack(
                    children: [
                      Positioned(
                        left: 37,
                        top: 48,
                        child: Container(
                          width: 328,
                          height: 212,
                          decoration: ShapeDecoration(
                            gradient: LinearGradient(
                              begin: Alignment(0.00, 0.50),
                              end: Alignment(1.00, 0.50),
                              colors: [const Color(0xFFF97316), const Color(0xFFEA580C)],
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Stack(
                            children: [
                              Positioned(
                                left: 24,
                                top: 24,
                                child: Container(
                                  width: 280,
                                  height: 32,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 19.81,
                                        top: 0,
                                        child: SizedBox(
                                          width: 240.38,
                                          height: 32,
                                          child: Text(
                                            'Ready to get started?',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 20.40,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w700,
                                              height: 1.57,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 24,
                                top: 68,
                                child: Container(
                                  width: 280,
                                  height: 48,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 1.42,
                                        top: 1,
                                        child: SizedBox(
                                          width: 277.16,
                                          height: 21,
                                          child: Text(
                                            'Join thousands of users managing their ',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 13.60,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              height: 1.76,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 56.94,
                                        top: 25,
                                        child: SizedBox(
                                          width: 166.11,
                                          height: 21,
                                          child: Text(
                                            'family planning journey',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 13.60,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              height: 1.76,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                left: 61.83,
                                top: 140,
                                child: Container(
                                  width: 204.34,
                                  height: 48,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(9999),
                                    ),
                                  ),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 32,
                                        top: 13,
                                        child: SizedBox(
                                          width: 112.34,
                                          height: 21,
                                          child: Text(
                                            'Download Now',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: const Color(0xFFEA580C),
                                              fontSize: 13.60,
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w500,
                                              height: 1.76,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 152.34,
                                        top: 14,
                                        child: Container(
                                          width: 20,
                                          height: 20,
                                          clipBehavior: Clip.antiAlias,
                                          decoration: BoxDecoration(),
                                          child: Stack(),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 2063.76,
                child: Container(
                  width: 402,
                  height: 121,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 1,
                        color: const Color(0xFFE5E7EB),
                      ),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        left: 37,
                        top: 33,
                        child: Container(
                          width: 328,
                          height: 20,
                          child: Stack(
                            children: [
                              Positioned(
                                left: 110,
                                top: 0,
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(),
                                  child: Stack(),
                                ),
                              ),
                              Positioned(
                                left: 154,
                                top: 0,
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(),
                                  child: Stack(),
                                ),
                              ),
                              Positioned(
                                left: 198,
                                top: 0,
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(),
                                  child: Stack(),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        left: 37,
                        top: 69,
                        child: Container(
                          width: 328,
                          height: 20,
                          child: Stack(
                            children: [
                              Positioned(
                                left: 56.48,
                                top: 0,
                                child: SizedBox(
                                  width: 215.02,
                                  height: 19,
                                  child: Text(
                                    '© 2024 ComFP. All rights reserved.',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: const Color(0xFF6B7280),
                                      fontSize: 11.90,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      height: 1.68,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}