import 'package:flutter/material.dart';
import 'messaging.dart';

class ExpertSupportScreen extends StatefulWidget {
  const ExpertSupportScreen({super.key});

  @override
  State<ExpertSupportScreen> createState() => _ExpertSupportScreenState();
}

class _ExpertSupportScreenState extends State<ExpertSupportScreen> {
  String _selectedCategory = 'All';
  final List<String> _categories = [
    'All',
    'Consultation',
    'Emergency',
    'General',
    'Specialist',
  ];

  final List<Map<String, dynamic>> _experts = [
    {
      'name': 'Dr. <PERSON>',
      'title': 'Family Planning Specialist',
      'specialization': 'Contraceptive Methods & Reproductive Health',
      'experience': '12 years',
      'rating': 4.9,
      'reviews': 156,
      'availability': 'Available Now',
      'category': 'Specialist',
      'image': 'assets/images/doctor1.jpg',
      'languages': ['English', 'Spanish'],
      'consultationFee': '\$75',
      'responseTime': '< 15 min',
      'featured': true,
    },
    {
      'name': 'Dr. <PERSON>',
      'title': 'Reproductive Endocrinologist',
      'specialization': 'Hormonal Health & Fertility',
      'experience': '15 years',
      'rating': 4.8,
      'reviews': 203,
      'availability': 'Available Today',
      'category': 'Specialist',
      'image': 'assets/images/doctor2.jpg',
      'languages': ['English', 'Mandarin'],
      'consultationFee': '\$85',
      'responseTime': '< 30 min',
      'featured': true,
    },
    {
      'name': 'Nurse Emma Wilson',
      'title': 'Certified Family Planning Counselor',
      'specialization': 'Contraceptive Counseling & Education',
      'experience': '8 years',
      'rating': 4.7,
      'reviews': 89,
      'availability': 'Available Now',
      'category': 'Consultation',
      'image': 'assets/images/nurse1.jpg',
      'languages': ['English'],
      'consultationFee': '\$45',
      'responseTime': '< 10 min',
      'featured': false,
    },
    {
      'name': 'Dr. Priya Patel',
      'title': 'Women\'s Health Specialist',
      'specialization': 'Menstrual Health & PCOS Management',
      'experience': '10 years',
      'rating': 4.9,
      'reviews': 134,
      'availability': 'Available Tomorrow',
      'category': 'Specialist',
      'image': 'assets/images/doctor3.jpg',
      'languages': ['English', 'Hindi'],
      'consultationFee': '\$70',
      'responseTime': '< 20 min',
      'featured': false,
    },
  ];

  final List<Map<String, dynamic>> _supportOptions = [
    {
      'title': '24/7 Emergency Hotline',
      'description':
          'Immediate support for urgent reproductive health concerns',
      'icon': Icons.phone,
      'color': const Color(0xFFDC2626),
      'action': 'Call Now',
      'category': 'Emergency',
    },
    {
      'title': 'Live Chat Support',
      'description': 'Quick answers to your family planning questions',
      'icon': Icons.chat,
      'color': const Color(0xFF059669),
      'action': 'Start Chat',
      'category': 'General',
    },
    {
      'title': 'Video Consultation',
      'description': 'Face-to-face consultation with certified experts',
      'icon': Icons.video_call,
      'color': const Color(0xFF7C3AED),
      'action': 'Book Now',
      'category': 'Consultation',
    },
    {
      'title': 'Community Forum',
      'description': 'Connect with others and share experiences',
      'icon': Icons.forum,
      'color': const Color(0xFF0891B2),
      'action': 'Join Forum',
      'category': 'General',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildHeroSection(),
                    _buildSupportOptionsSection(),
                    _buildExpertsSection(),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFF3F4F6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Color(0xFF374151),
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Text(
              'Expert Support',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF111827),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFF3F4F6),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.notifications_outlined,
              color: Color(0xFF374151),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFF97316), Color(0xFFEA580C)],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Get Expert\nGuidance',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        height: 1.2,
                      ),
                    ),
                    SizedBox(height: 12),
                    Text(
                      'Connect with certified family planning\nexperts for personalized advice and\nsupport on your reproductive health journey',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.support_agent,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              _buildStatItem('50+', 'Certified\nExperts'),
              const SizedBox(width: 24),
              _buildStatItem('24/7', 'Available\nSupport'),
              const SizedBox(width: 24),
              _buildStatItem('4.8★', 'Average\nRating'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.9),
            height: 1.2,
          ),
        ),
      ],
    );
  }

  Widget _buildSupportOptionsSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Support Options',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          ...(_supportOptions.map((option) => _buildSupportOptionCard(option))),
        ],
      ),
    );
  }

  Widget _buildSupportOptionCard(Map<String, dynamic> option) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: option['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(option['icon'], color: option['color'], size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  option['title'],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF111827),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  option['description'],
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () => _handleSupportAction(option),
            style: ElevatedButton.styleFrom(
              backgroundColor: option['color'],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              option['action'],
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpertsSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Expanded(
                child: Text(
                  'Available Experts',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF111827),
                  ),
                ),
              ),
              TextButton(
                onPressed: () => _showAllExperts(),
                child: const Text(
                  'View All',
                  style: TextStyle(
                    color: Color(0xFFF97316),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildCategoryFilter(),
          const SizedBox(height: 16),
          ...(_getFilteredExperts().map((expert) => _buildExpertCard(expert))),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children:
            _categories.map((category) {
              bool isSelected = _selectedCategory == category;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFFF97316) : Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color:
                          isSelected
                              ? const Color(0xFFF97316)
                              : const Color(0xFFE5E7EB),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    category,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color:
                          isSelected ? Colors.white : const Color(0xFF6B7280),
                    ),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredExperts() {
    if (_selectedCategory == 'All') {
      return _experts;
    }
    return _experts
        .where((expert) => expert['category'] == _selectedCategory)
        .toList();
  }

  Widget _buildExpertCard(Map<String, dynamic> expert) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: const Color(0xFFF97316),
                child: Text(
                  expert['name'].split(' ').map((n) => n[0]).join(''),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            expert['name'],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF111827),
                            ),
                          ),
                        ),
                        if (expert['featured'])
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF97316),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'Featured',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      expert['title'],
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFFF97316),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      expert['specialization'],
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildExpertStat(
                Icons.star,
                '${expert['rating']}',
                '(${expert['reviews']} reviews)',
              ),
              const SizedBox(width: 16),
              _buildExpertStat(
                Icons.access_time,
                expert['responseTime'],
                'Response time',
              ),
              const Spacer(),
              Text(
                expert['consultationFee'],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFF97316),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _showExpertDetails(expert),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Color(0xFFF97316)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'View Profile',
                    style: TextStyle(color: Color(0xFFF97316)),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _bookConsultation(expert),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFF97316),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Book Now'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExpertStat(IconData icon, String value, String label) {
    return Row(
      children: [
        Icon(icon, size: 16, color: const Color(0xFF6B7280)),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFF111827),
              ),
            ),
            Text(
              label,
              style: const TextStyle(fontSize: 10, color: Color(0xFF6B7280)),
            ),
          ],
        ),
      ],
    );
  }

  void _handleSupportAction(Map<String, dynamic> option) {
    switch (option['title']) {
      case '24/7 Emergency Hotline':
        _showEmergencyDialog();
        break;
      case 'Live Chat Support':
        _startLiveChat();
        break;
      case 'Video Consultation':
        _bookVideoConsultation();
        break;
      case 'Community Forum':
        _openCommunityForum();
        break;
    }
  }

  void _showAllExperts() {
    // Navigate to a dedicated all experts page or show modal
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.9,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    'All Available Experts',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF111827),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _experts.length,
                    itemBuilder:
                        (context, index) => _buildExpertCard(_experts[index]),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _showExpertDetails(Map<String, dynamic> expert) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: const Color(0xFFE5E7EB),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: const Color(0xFFF97316),
                        child: Text(
                          expert['name'].split(' ').map((n) => n[0]).join(''),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              expert['name'],
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF111827),
                              ),
                            ),
                            Text(
                              expert['title'],
                              style: const TextStyle(
                                fontSize: 16,
                                color: Color(0xFFF97316),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '${expert['experience']} experience',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF6B7280),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  _buildDetailSection(
                    'Specialization',
                    expert['specialization'],
                  ),
                  _buildDetailSection(
                    'Languages',
                    expert['languages'].join(', '),
                  ),
                  _buildDetailSection('Availability', expert['availability']),
                  _buildDetailSection(
                    'Consultation Fee',
                    expert['consultationFee'],
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _bookConsultation(expert);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFF97316),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Book Consultation',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildDetailSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF374151),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: const TextStyle(fontSize: 16, color: Color(0xFF111827)),
          ),
        ],
      ),
    );
  }

  void _bookConsultation(Map<String, dynamic> expert) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Book Consultation with ${expert['name']}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Consultation Fee: ${expert['consultationFee']}'),
                const SizedBox(height: 8),
                Text('Response Time: ${expert['responseTime']}'),
                const SizedBox(height: 16),
                const Text('Select your preferred time slot:'),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'morning',
                      child: Text('Morning (9:00 AM - 12:00 PM)'),
                    ),
                    DropdownMenuItem(
                      value: 'afternoon',
                      child: Text('Afternoon (1:00 PM - 5:00 PM)'),
                    ),
                    DropdownMenuItem(
                      value: 'evening',
                      child: Text('Evening (6:00 PM - 9:00 PM)'),
                    ),
                  ],
                  onChanged: (value) {},
                  hint: const Text('Select time slot'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Consultation booked with ${expert['name']}',
                      ),
                      backgroundColor: const Color(0xFFF97316),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFF97316),
                ),
                child: const Text(
                  'Book Now',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  void _showEmergencyDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Emergency Support'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('For immediate medical emergencies, please call:'),
                SizedBox(height: 16),
                Text(
                  '911',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFDC2626),
                  ),
                ),
                SizedBox(height: 16),
                Text('For reproductive health emergencies:'),
                SizedBox(height: 8),
                Text(
                  '1-800-HEALTH',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFF97316),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Here you would implement actual calling functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Calling emergency hotline...'),
                      backgroundColor: Color(0xFFDC2626),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFDC2626),
                ),
                child: const Text(
                  'Call Now',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  void _startLiveChat() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const MessagingScreen()),
    );
  }

  void _bookVideoConsultation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening video consultation booking...'),
        backgroundColor: Color(0xFF7C3AED),
      ),
    );
    // Here you would implement video consultation booking
  }

  void _openCommunityForum() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening community forum...'),
        backgroundColor: Color(0xFF0891B2),
      ),
    );
    // Here you would navigate to community forum
  }
}
