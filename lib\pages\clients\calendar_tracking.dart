import 'package:flutter/material.dart';
import 'daily_tracking.dart';
import 'insights.dart';
import 'cycle_tracking.dart';

class CalendarTrackingScreen extends StatefulWidget {
  const CalendarTrackingScreen({super.key});

  @override
  State<CalendarTrackingScreen> createState() => _CalendarTrackingScreenState();
}

class _CalendarTrackingScreenState extends State<CalendarTrackingScreen> {
  int _selectedTab = 3; // Calendar is selected
  DateTime _currentMonth = DateTime.now();
  DateTime? _selectedDate;

  // Simple frontend data - no backend needed
  Map<DateTime, String> get _cycleData {
    final now = DateTime.now();
    return {
      DateTime(now.year, now.month, 1): 'period',
      DateTime(now.year, now.month, 2): 'period',
      DateTime(now.year, now.month, 3): 'period',
      DateTime(now.year, now.month, 4): 'period',
      DateTime(now.year, now.month, 5): 'period',
      DateTime(now.year, now.month, 14): 'ovulation',
      DateTime(now.year, now.month, 28): 'predicted',
      DateTime(now.year, now.month, 29): 'predicted',
      DateTime(now.year, now.month, 30): 'predicted',
      DateTime(now.year, now.month, 31): 'predicted',
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      body: Column(
        children: [
          // Header Section with Navigation
          _buildHeaderWithNavigation(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Calendar Section
                  _buildCalendarSection(),

                  // Legend Section
                  _buildLegendSection(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderWithNavigation() {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [Color(0xFFF97316), Color(0xFFEA580C)],
        ),
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Navigation Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                        size: 18,
                      ),
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                  const Expanded(
                    child: Text(
                      'Calendar',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 48), // Balance the back button
                ],
              ),
            ),

            // Header Content
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 8, 24, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Advanced Cycle\nTracking',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Monitor your menstrual cycle with\nintelligent predictions and\npersonalized insights',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),

            // Tab Navigation within header
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Row(
                children: [
                  _buildTabButton('Overview', 0),
                  const SizedBox(width: 8),
                  _buildTabButton('Daily Tracking', 1),
                  const SizedBox(width: 8),
                  _buildTabButton('Insights', 2),
                  const SizedBox(width: 8),
                  _buildTabButton('Calendar', 3),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabButton(String title, int index) {
    bool isSelected = _selectedTab == index;
    return GestureDetector(
      onTap: () {
        if (index == 0) {
          // Navigate back to Cycle Tracking (Overview)
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const CycleTrackingScreen(),
            ),
          );
        } else if (index == 1) {
          // Navigate to Daily Tracking page
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const DailyTrackingScreen(),
            ),
          );
        } else if (index == 2) {
          // Navigate to Insights page
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const InsightsScreen()),
          );
        } else {
          setState(() {
            _selectedTab = index;
          });
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF97316) : const Color(0xFFFFF7ED),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : const Color(0xFFEA580C),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Month Navigation
          _buildMonthNavigation(),
          const SizedBox(height: 20),

          // Calendar Grid
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  Widget _buildMonthNavigation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(
                _currentMonth.year,
                _currentMonth.month - 1,
              );
            });
          },
          icon: const Icon(Icons.chevron_left, color: Color(0xFF6B7280)),
        ),
        Text(
          _getMonthYearString(_currentMonth),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF111827),
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(
                _currentMonth.year,
                _currentMonth.month + 1,
              );
            });
          },
          icon: const Icon(Icons.chevron_right, color: Color(0xFF6B7280)),
        ),
      ],
    );
  }

  Widget _buildCalendarGrid() {
    return Column(
      children: [
        // Weekday headers
        Row(
          children:
              ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
                  .map(
                    (day) => Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF6B7280),
                          ),
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),
        const SizedBox(height: 12),

        // Calendar days
        ..._buildCalendarWeeks(),
      ],
    );
  }

  List<Widget> _buildCalendarWeeks() {
    List<Widget> weeks = [];
    DateTime firstDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month,
      1,
    );
    DateTime lastDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month + 1,
      0,
    );

    // Calculate the first day to show (might be from previous month)
    DateTime firstDayToShow = firstDayOfMonth.subtract(
      Duration(days: firstDayOfMonth.weekday % 7),
    );

    DateTime currentDay = firstDayToShow;

    while (currentDay.isBefore(lastDayOfMonth) ||
        currentDay.month == _currentMonth.month) {
      List<Widget> weekDays = [];

      for (int i = 0; i < 7; i++) {
        weekDays.add(_buildCalendarDay(currentDay));
        currentDay = currentDay.add(const Duration(days: 1));
      }

      weeks.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(children: weekDays),
        ),
      );

      if (currentDay.month != _currentMonth.month && currentDay.day > 7) break;
    }

    return weeks;
  }

  Widget _buildCalendarDay(DateTime date) {
    bool isCurrentMonth = date.month == _currentMonth.month;
    bool isToday = _isSameDay(date, DateTime.now());
    bool isSelected = _selectedDate != null && _isSameDay(date, _selectedDate!);
    String? cycleType = _cycleData[DateTime(date.year, date.month, date.day)];

    Color? backgroundColor;
    Color? textColor =
        isCurrentMonth ? const Color(0xFF111827) : const Color(0xFFD1D5DB);

    if (isSelected) {
      backgroundColor = const Color(0xFFF97316);
      textColor = Colors.white;
    } else if (isToday) {
      backgroundColor = const Color(0xFFFFF7ED);
      textColor = const Color(0xFFF97316);
    } else if (cycleType != null) {
      switch (cycleType) {
        case 'period':
          backgroundColor = const Color(0xFFFFE4E6);
          textColor = const Color(0xFFDC2626);
          break;
        case 'ovulation':
          backgroundColor = const Color(0xFFDCFCE7);
          textColor = const Color(0xFF059669);
          break;
        case 'predicted':
          backgroundColor = const Color(0xFFEDE9FE);
          textColor = const Color(0xFF7C3AED);
          break;
      }
    }

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedDate = date;
          });
          if (isCurrentMonth) {
            _showDayDetails(date);
          }
        },
        child: Container(
          height: 40,
          margin: const EdgeInsets.all(1),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border:
                isToday && !isSelected
                    ? Border.all(color: const Color(0xFFF97316), width: 2)
                    : null,
          ),
          child: Stack(
            children: [
              Center(
                child: Text(
                  '${date.day}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isToday ? FontWeight.bold : FontWeight.w500,
                    color: textColor,
                  ),
                ),
              ),
              if (cycleType == 'period')
                Positioned(
                  bottom: 2,
                  right: 2,
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: Color(0xFFDC2626),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLegendSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Calendar Legend',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildLegendItem(
                  'Period Days',
                  const Color(0xFFFFE4E6),
                  const Color(0xFFDC2626),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildLegendItem(
                  'Fertile Window',
                  const Color(0xFFDCFCE7),
                  const Color(0xFF059669),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildLegendItem(
                  'Ovulation Day',
                  const Color(0xFFDCFCE7),
                  const Color(0xFF059669),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildLegendItem(
                  'Predicted Days',
                  const Color(0xFFEDE9FE),
                  const Color(0xFF7C3AED),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(
    String title,
    Color backgroundColor,
    Color textColor,
  ) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(fontSize: 12, color: Color(0xFF6B7280)),
          ),
        ),
      ],
    );
  }

  void _showDayDetails(DateTime date) {
    String? cycleType = _cycleData[DateTime(date.year, date.month, date.day)];
    String dayInfo = _getDayInfo(cycleType);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _getFormattedDate(date),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF111827),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Color(0xFF6B7280)),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  dayInfo,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const DailyTrackingScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFF97316),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Log Today\'s Data',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  String _getDayInfo(String? cycleType) {
    switch (cycleType) {
      case 'period':
        return 'Period day. Track your flow, symptoms, and mood to get better insights about your cycle.';
      case 'ovulation':
        return 'Ovulation day. This is your most fertile day. Perfect time to track temperature and symptoms.';
      case 'predicted':
        return 'Predicted period day. Based on your cycle history, your period might start around this time.';
      default:
        return 'Regular day in your cycle. You can still log symptoms, mood, and other health data.';
    }
  }

  String _getFormattedDate(DateTime date) {
    List<String> months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _getMonthYearString(DateTime date) {
    List<String> months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
