import 'package:flutter/material.dart';
import 'daily_tracking.dart';
import 'calendar_tracking.dart';
import 'cycle_tracking.dart';
import 'insights.dart';

class SmartRemindersScreen extends StatefulWidget {
  const SmartRemindersScreen({super.key});

  @override
  State<SmartRemindersScreen> createState() => _SmartRemindersScreenState();
}

class _SmartRemindersScreenState extends State<SmartRemindersScreen> {
  int _selectedTab =
      1; // Daily Tracking is selected (reminders are part of daily routine)

  final Map<String, bool> _reminderSettings = {
    'period_prediction': true,
    'ovulation_alert': true,
    'daily_logging': false,
    'medication': true,
    'symptoms': false,
    'mood_check': true,
    'temperature': false,
    'weight': false,
  };

  final Map<String, String> _reminderTimes = {
    'period_prediction': '9:00 AM',
    'ovulation_alert': '8:00 AM',
    'daily_logging': '7:00 PM',
    'medication': '8:00 AM',
    'symptoms': '6:00 PM',
    'mood_check': '9:00 PM',
    'temperature': '7:00 AM',
    'weight': '7:30 AM',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      body: Column(
        children: [
          // Header Section with Navigation
          _buildHeaderWithNavigation(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Active Reminders Section
                  _buildActiveRemindersSection(),

                  // Cycle Reminders Section
                  _buildCycleRemindersSection(),

                  // Daily Reminders Section
                  _buildDailyRemindersSection(),

                  // Notification Settings Section
                  _buildNotificationSettingsSection(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderWithNavigation() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFF97316), Color(0xFFEA580C)],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Top navigation bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                  ),
                  const Expanded(
                    child: Text(
                      'Smart Reminders',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.notifications_active,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),

            // Header Content
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 8, 24, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Never Miss\nImportant Moments',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Intelligent reminders that adapt to your\ncycle and help you stay on top of\nyour health routine',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),

            // Tab Navigation within header
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Row(
                children: [
                  _buildTabButton('Overview', 0),
                  const SizedBox(width: 8),
                  _buildTabButton('Daily Tracking', 1),
                  const SizedBox(width: 8),
                  _buildTabButton('Insights', 2),
                  const SizedBox(width: 8),
                  _buildTabButton('Calendar', 3),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabButton(String title, int index) {
    bool isSelected = _selectedTab == index;
    return GestureDetector(
      onTap: () {
        if (index == 0) {
          // Navigate back to Cycle Tracking (Overview)
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const CycleTrackingScreen(),
            ),
          );
        } else if (index == 1) {
          // Navigate to Daily Tracking page
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const DailyTrackingScreen(),
            ),
          );
        } else if (index == 2) {
          // Navigate to Insights page
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const InsightsScreen()),
          );
        } else if (index == 3) {
          // Navigate to Calendar page
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const CalendarTrackingScreen(),
            ),
          );
        } else {
          setState(() {
            _selectedTab = index;
          });
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color:
              isSelected ? Colors.white : Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? const Color(0xFFF97316) : Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildActiveRemindersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFFFF7ED),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.notifications_active,
                  color: Color(0xFFF97316),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Active Reminders',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF111827),
                      ),
                    ),
                    Text(
                      '4 reminders active today',
                      style: TextStyle(fontSize: 12, color: Color(0xFF6B7280)),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildActiveReminderItem(
            'Period Prediction',
            'Your period is expected in 14 days',
            '9:00 AM',
            Icons.calendar_today,
            const Color(0xFFDC2626),
          ),
          const SizedBox(height: 12),
          _buildActiveReminderItem(
            'Ovulation Alert',
            'Fertile window starts in 7 days',
            '8:00 AM',
            Icons.favorite,
            const Color(0xFF059669),
          ),
          const SizedBox(height: 12),
          _buildActiveReminderItem(
            'Medication',
            'Time for your daily supplement',
            '8:00 AM',
            Icons.medication,
            const Color(0xFF7C3AED),
          ),
          const SizedBox(height: 12),
          _buildActiveReminderItem(
            'Mood Check',
            'How are you feeling today?',
            '9:00 PM',
            Icons.mood,
            const Color(0xFFF97316),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveReminderItem(
    String title,
    String description,
    String time,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF111827),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              time,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCycleRemindersSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cycle Reminders',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          _buildReminderToggle(
            'Period Prediction',
            'Get notified 3 days before your period',
            'period_prediction',
            Icons.calendar_today,
            const Color(0xFFDC2626),
          ),
          const SizedBox(height: 12),
          _buildReminderToggle(
            'Ovulation Alert',
            'Fertile window and ovulation notifications',
            'ovulation_alert',
            Icons.favorite,
            const Color(0xFF059669),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyRemindersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Daily Reminders',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          _buildReminderToggle(
            'Daily Logging',
            'Remind me to log my daily data',
            'daily_logging',
            Icons.edit_note,
            const Color(0xFFF97316),
          ),
          const SizedBox(height: 12),
          _buildReminderToggle(
            'Medication',
            'Daily supplement and medication reminders',
            'medication',
            Icons.medication,
            const Color(0xFF7C3AED),
          ),
          const SizedBox(height: 12),
          _buildReminderToggle(
            'Symptoms',
            'Check in on how you\'re feeling',
            'symptoms',
            Icons.trending_up,
            const Color(0xFFEAB308),
          ),
          const SizedBox(height: 12),
          _buildReminderToggle(
            'Mood Check',
            'Daily mood and emotional wellness check',
            'mood_check',
            Icons.mood,
            const Color(0xFFF97316),
          ),
          const SizedBox(height: 12),
          _buildReminderToggle(
            'Temperature',
            'Morning basal body temperature',
            'temperature',
            Icons.thermostat,
            const Color(0xFF06B6D4),
          ),
          const SizedBox(height: 12),
          _buildReminderToggle(
            'Weight',
            'Daily weight tracking reminder',
            'weight',
            Icons.monitor_weight,
            const Color(0xFF8B5CF6),
          ),
        ],
      ),
    );
  }

  Widget _buildReminderToggle(
    String title,
    String description,
    String key,
    IconData icon,
    Color color,
  ) {
    bool isEnabled = _reminderSettings[key] ?? false;
    String time = _reminderTimes[key] ?? '9:00 AM';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            isEnabled ? color.withValues(alpha: 0.05) : const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isEnabled
                  ? color.withValues(alpha: 0.2)
                  : const Color(0xFFE5E7EB),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color:
                  isEnabled
                      ? color.withValues(alpha: 0.1)
                      : const Color(0xFFE5E7EB),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: isEnabled ? color : const Color(0xFF9CA3AF),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color:
                        isEnabled
                            ? const Color(0xFF111827)
                            : const Color(0xFF6B7280),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                  ),
                ),
                if (isEnabled) ...[
                  const SizedBox(height: 4),
                  GestureDetector(
                    onTap: () => _showTimePickerDialog(key, time),
                    child: Text(
                      'Reminder at $time',
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: color,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Switch(
            value: isEnabled,
            onChanged: (value) {
              setState(() {
                _reminderSettings[key] = value;
              });
            },
            activeColor: color,
            activeTrackColor: color.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettingsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Notification Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF111827),
            ),
          ),
          const SizedBox(height: 16),
          _buildSettingItem(
            'Sound',
            'Play notification sound',
            Icons.volume_up,
            true,
          ),
          const SizedBox(height: 12),
          _buildSettingItem(
            'Vibration',
            'Vibrate on notifications',
            Icons.vibration,
            true,
          ),
          const SizedBox(height: 12),
          _buildSettingItem(
            'Do Not Disturb',
            'Respect quiet hours (10 PM - 7 AM)',
            Icons.do_not_disturb,
            false,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF7ED),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Color(0xFFF97316),
                  size: 20,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Smart reminders adapt to your cycle patterns and become more personalized over time',
                    style: TextStyle(fontSize: 12, color: Color(0xFF6B7280)),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    String title,
    String description,
    IconData icon,
    bool value,
  ) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFFF9FAFB),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: const Color(0xFF6B7280), size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111827),
                ),
              ),
              Text(
                description,
                style: const TextStyle(fontSize: 12, color: Color(0xFF6B7280)),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: (newValue) {
            // Handle setting change
          },
          activeColor: const Color(0xFFF97316),
          activeTrackColor: const Color(0xFFF97316).withValues(alpha: 0.3),
        ),
      ],
    );
  }

  void _showTimePickerDialog(String key, String currentTime) {
    showTimePicker(context: context, initialTime: TimeOfDay.now()).then((time) {
      if (time != null) {
        setState(() {
          _reminderTimes[key] = time.format(context);
        });
      }
    });
  }
}
